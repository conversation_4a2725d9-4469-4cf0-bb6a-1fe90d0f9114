const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:8081', 'http://localhost:3000', 'http://localhost:19006'],
  credentials: true
}));
app.use(express.json());

// Mock data
const mockUsers = [
  {
    _id: '1',
    firstName: 'Super',
    lastName: 'Admin',
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    role: 'super_admin',
    status: 'active'
  },
  {
    _id: '2',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active'
  },
  {
    _id: '3',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    role: 'user',
    status: 'active'
  }
];

const mockVendors = [
  {
    _id: '1',
    businessName: 'Elite Catering',
    email: '<EMAIL>',
    status: 'pending',
    businessInfo: { category: 'Catering' }
  },
  {
    _id: '2',
    businessName: 'Perfect Photos',
    email: '<EMAIL>',
    status: 'approved',
    businessInfo: { category: 'Photography' }
  },
  {
    _id: '3',
    businessName: 'Dream Decorations',
    email: '<EMAIL>',
    status: 'pending',
    businessInfo: { category: 'Decoration' }
  }
];

const mockStats = {
  overview: {
    totalUsers: 1247,
    totalVendors: 89,
    totalBookings: 456,
    totalEvents: 123,
    totalRevenue: 89750,
    activeUsers: 1156,
    pendingVendors: 12
  }
};

// JWT Secret
const JWT_SECRET = 'your-secret-key';

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required',
      code: 'TOKEN_REQUIRED'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token',
        code: 'INVALID_TOKEN'
      });
    }
    req.user = user;
    next();
  });
};

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'EventEase Admin Backend is running',
    timestamp: new Date().toISOString()
  });
});

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { email, password } = req.body;

  const user = mockUsers.find(u => u.email === email && u.password === password);
  
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Invalid email or password',
      code: 'INVALID_CREDENTIALS'
    });
  }

  const token = jwt.sign(
    { userId: user._id, email: user.email, role: user.role },
    JWT_SECRET,
    { expiresIn: '24h' }
  );

  const { password: _, ...userWithoutPassword } = user;

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: userWithoutPassword,
      token
    }
  });
});

app.post('/api/auth/register', (req, res) => {
  const { firstName, lastName, email, password, adminCode } = req.body;

  if (adminCode !== 'ADMIN2024') {
    return res.status(400).json({
      success: false,
      message: 'Invalid admin registration code',
      code: 'INVALID_ADMIN_CODE'
    });
  }

  const existingUser = mockUsers.find(u => u.email === email);
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: 'Email already exists',
      code: 'EMAIL_EXISTS'
    });
  }

  const newUser = {
    _id: String(mockUsers.length + 1),
    firstName,
    lastName,
    email,
    password,
    role: 'admin',
    status: 'active'
  };

  mockUsers.push(newUser);

  const token = jwt.sign(
    { userId: newUser._id, email: newUser.email, role: newUser.role },
    JWT_SECRET,
    { expiresIn: '24h' }
  );

  const { password: _, ...userWithoutPassword } = newUser;

  res.json({
    success: true,
    message: 'Registration successful',
    data: {
      user: userWithoutPassword,
      token
    }
  });
});

// Dashboard routes
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Dashboard statistics retrieved successfully',
    data: mockStats
  });
});

// User routes
app.get('/api/users', authenticateToken, (req, res) => {
  const usersWithoutPasswords = mockUsers.map(({ password, ...user }) => user);
  
  res.json({
    success: true,
    message: 'Users retrieved successfully',
    data: {
      users: usersWithoutPasswords,
      pagination: {
        page: 1,
        limit: 10,
        total: mockUsers.length,
        totalPages: 1
      }
    }
  });
});

// Vendor routes
app.get('/api/vendors', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Vendors retrieved successfully',
    data: {
      vendors: mockVendors,
      pagination: {
        page: 1,
        limit: 10,
        total: mockVendors.length,
        totalPages: 1
      }
    }
  });
});

// Events routes
app.get('/api/events', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Events endpoint - Coming soon',
    data: {
      events: [],
      message: 'Events management functionality will be implemented here'
    }
  });
});

app.get('/api/events/stats', authenticateToken, (req, res) => {
  const stats = {
    totalEvents: Math.floor(Math.random() * 200) + 50,
    activeEvents: Math.floor(Math.random() * 100) + 20,
    upcomingEvents: Math.floor(Math.random() * 50) + 10,
    completedEvents: Math.floor(Math.random() * 150) + 30
  };

  res.json({
    success: true,
    message: 'Event statistics retrieved successfully',
    data: stats
  });
});

// Bookings routes
app.get('/api/bookings', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Bookings endpoint - Coming soon',
    data: {
      bookings: [],
      message: 'Bookings management functionality will be implemented here'
    }
  });
});

app.get('/api/bookings/stats', authenticateToken, (req, res) => {
  const stats = {
    totalBookings: Math.floor(Math.random() * 500) + 100,
    pendingBookings: Math.floor(Math.random() * 50) + 10,
    confirmedBookings: Math.floor(Math.random() * 300) + 50,
    cancelledBookings: Math.floor(Math.random() * 100) + 20,
    totalRevenue: Math.floor(Math.random() * 100000) + 10000
  };

  res.json({
    success: true,
    message: 'Booking statistics retrieved successfully',
    data: stats
  });
});

// Analytics routes
app.get('/api/analytics', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Analytics endpoint - Coming soon',
    data: {
      analytics: {},
      message: 'Advanced analytics functionality will be implemented here'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: req.originalUrl
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    code: 'INTERNAL_ERROR'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 EventEase Admin Backend (Simple) running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log('📧 Default admin: <EMAIL>');
  console.log('🔑 Default password: SuperAdmin123!');
});

module.exports = app;
