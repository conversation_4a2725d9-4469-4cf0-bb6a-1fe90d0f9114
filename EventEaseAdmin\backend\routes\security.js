const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { authenticateToken, requireRole } = require('../middleware/auth');
const User = require('../models/User');
const SecurityLog = require('../models/SecurityLog');
const router = express.Router();

// Rate limiting for security endpoints
const securityLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // limit each IP to 10 requests per windowMs
    message: {
        success: false,
        message: 'Too many security requests, please try again later.'
    }
});

// Security log model
const logSecurityEvent = async (userId, action, details, ipAddress, userAgent) => {
    try {
        await SecurityLog.create({
            userId,
            action,
            details,
            ipAddress,
            userAgent,
            timestamp: new Date()
        });
    } catch (error) {
        console.error('Failed to log security event:', error);
    }
};

// @route   GET /api/security/logs
// @desc    Get security logs (Admin only)
// @access  Private
router.get('/logs', authenticateToken, requireRole(['super_admin', 'admin']), async (req, res) => {
    try {
        const { page = 1, limit = 50, action, userId, startDate, endDate } = req.query;
        
        const query = {};
        if (action) query.action = action;
        if (userId) query.userId = userId;
        if (startDate || endDate) {
            query.timestamp = {};
            if (startDate) query.timestamp.$gte = new Date(startDate);
            if (endDate) query.timestamp.$lte = new Date(endDate);
        }

        const logs = await SecurityLog.find(query)
            .populate('userId', 'firstName lastName email')
            .sort({ timestamp: -1 })
            .limit(limit * 1)
            .skip((page - 1) * limit);

        const total = await SecurityLog.countDocuments(query);

        res.json({
            success: true,
            data: {
                logs,
                pagination: {
                    current: page,
                    pages: Math.ceil(total / limit),
                    total
                }
            }
        });
    } catch (error) {
        console.error('Get security logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve security logs'
        });
    }
});

// @route   POST /api/security/change-password
// @desc    Change user password
// @access  Private
router.post('/change-password', securityLimiter, authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword, confirmPassword } = req.body;
        const userId = req.user.id;

        // Validation
        if (!currentPassword || !newPassword || !confirmPassword) {
            return res.status(400).json({
                success: false,
                message: 'All password fields are required'
            });
        }

        if (newPassword !== confirmPassword) {
            return res.status(400).json({
                success: false,
                message: 'New passwords do not match'
            });
        }

        if (newPassword.length < 8) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 8 characters long'
            });
        }

        // Password strength validation
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
        if (!passwordRegex.test(newPassword)) {
            return res.status(400).json({
                success: false,
                message: 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            await logSecurityEvent(
                userId,
                'INVALID_PASSWORD_CHANGE_ATTEMPT',
                'User attempted to change password with incorrect current password',
                req.ip,
                req.get('User-Agent')
            );
            
            return res.status(400).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Hash new password
        const saltRounds = 12;
        const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await User.findByIdAndUpdate(userId, {
            password: hashedNewPassword,
            passwordChangedAt: new Date()
        });

        // Log security event
        await logSecurityEvent(
            userId,
            'PASSWORD_CHANGED',
            'User successfully changed password',
            req.ip,
            req.get('User-Agent')
        );

        res.json({
            success: true,
            message: 'Password changed successfully'
        });

    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to change password'
        });
    }
});

// @route   POST /api/security/enable-2fa
// @desc    Enable two-factor authentication
// @access  Private
router.post('/enable-2fa', securityLimiter, authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const { secret, token } = req.body;

        if (!secret || !token) {
            return res.status(400).json({
                success: false,
                message: 'Secret and token are required'
            });
        }

        // Here you would verify the TOTP token with the secret
        // For now, we'll simulate this
        const isValidToken = token.length === 6 && /^\d+$/.test(token);
        
        if (!isValidToken) {
            return res.status(400).json({
                success: false,
                message: 'Invalid authentication token'
            });
        }

        await User.findByIdAndUpdate(userId, {
            twoFactorEnabled: true,
            twoFactorSecret: secret,
            twoFactorEnabledAt: new Date()
        });

        await logSecurityEvent(
            userId,
            'TWO_FACTOR_ENABLED',
            'User enabled two-factor authentication',
            req.ip,
            req.get('User-Agent')
        );

        res.json({
            success: true,
            message: 'Two-factor authentication enabled successfully'
        });

    } catch (error) {
        console.error('Enable 2FA error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to enable two-factor authentication'
        });
    }
});

// @route   POST /api/security/disable-2fa
// @desc    Disable two-factor authentication
// @access  Private
router.post('/disable-2fa', securityLimiter, authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const { password } = req.body;

        if (!password) {
            return res.status(400).json({
                success: false,
                message: 'Password is required to disable 2FA'
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            await logSecurityEvent(
                userId,
                'INVALID_2FA_DISABLE_ATTEMPT',
                'User attempted to disable 2FA with incorrect password',
                req.ip,
                req.get('User-Agent')
            );
            
            return res.status(400).json({
                success: false,
                message: 'Incorrect password'
            });
        }

        await User.findByIdAndUpdate(userId, {
            twoFactorEnabled: false,
            twoFactorSecret: null,
            twoFactorDisabledAt: new Date()
        });

        await logSecurityEvent(
            userId,
            'TWO_FACTOR_DISABLED',
            'User disabled two-factor authentication',
            req.ip,
            req.get('User-Agent')
        );

        res.json({
            success: true,
            message: 'Two-factor authentication disabled successfully'
        });

    } catch (error) {
        console.error('Disable 2FA error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to disable two-factor authentication'
        });
    }
});

// @route   GET /api/security/sessions
// @desc    Get active user sessions
// @access  Private
router.get('/sessions', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        
        // Get recent login logs for this user
        const sessions = await SecurityLog.find({
            userId,
            action: 'LOGIN_SUCCESS'
        })
        .sort({ timestamp: -1 })
        .limit(10);

        res.json({
            success: true,
            data: sessions
        });

    } catch (error) {
        console.error('Get sessions error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve sessions'
        });
    }
});

// @route   GET /api/security/dashboard
// @desc    Get security dashboard data
// @access  Private
router.get('/dashboard', authenticateToken, requireRole(['super_admin', 'admin']), async (req, res) => {
    try {
        const now = new Date();
        const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        // Get security metrics
        const [
            totalLogs,
            logsLast24h,
            logsLast7d,
            failedLogins24h,
            suspiciousActivity,
            activeUsers
        ] = await Promise.all([
            SecurityLog.countDocuments(),
            SecurityLog.countDocuments({ timestamp: { $gte: last24Hours } }),
            SecurityLog.countDocuments({ timestamp: { $gte: last7Days } }),
            SecurityLog.countDocuments({
                action: 'LOGIN_FAILED',
                timestamp: { $gte: last24Hours }
            }),
            SecurityLog.countDocuments({
                action: { $in: ['INVALID_PASSWORD_CHANGE_ATTEMPT', 'INVALID_2FA_DISABLE_ATTEMPT'] },
                timestamp: { $gte: last7Days }
            }),
            User.countDocuments({ status: 'active' })
        ]);

        // Get recent security events
        const recentEvents = await SecurityLog.find()
            .populate('userId', 'firstName lastName email')
            .sort({ timestamp: -1 })
            .limit(10);

        res.json({
            success: true,
            data: {
                metrics: {
                    totalLogs,
                    logsLast24h,
                    logsLast7d,
                    failedLogins24h,
                    suspiciousActivity,
                    activeUsers
                },
                recentEvents
            }
        });

    } catch (error) {
        console.error('Get security dashboard error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve security dashboard data'
        });
    }
});

module.exports = router;
