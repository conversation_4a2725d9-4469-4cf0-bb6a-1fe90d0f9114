const User = require('../models/User');

const createDefaultAdmin = async () => {
  try {
    // Check if any super admin exists
    const existingSuperAdmin = await User.findOne({ role: 'super_admin' });
    
    if (existingSuperAdmin) {
      console.log('✅ Super admin already exists');
      return;
    }

    // Create default super admin
    const defaultAdmin = new User({
      firstName: 'Super',
      lastName: 'Admin',
      email: process.env.SUPER_ADMIN_EMAIL || '<EMAIL>',
      password: process.env.SUPER_ADMIN_PASSWORD || 'SuperAdmin123!',
      role: 'super_admin',
      status: 'active',
      emailVerified: true,
      metadata: {
        ipAddress: '127.0.0.1',
        userAgent: 'System'
      }
    });

    await defaultAdmin.save();
    console.log('✅ Default super admin created successfully');
    console.log(`📧 Email: ${defaultAdmin.email}`);
    console.log(`🔑 Password: ${process.env.SUPER_ADMIN_PASSWORD || 'SuperAdmin123!'}`);
    console.log('⚠️  Please change the default password after first login');

  } catch (error) {
    if (error.code === 11000) {
      console.log('✅ Super admin already exists');
    } else {
      console.error('❌ Error creating default admin:', error.message);
    }
  }
};

module.exports = createDefaultAdmin;
