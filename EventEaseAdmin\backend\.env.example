# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/eventease_admin
MONGODB_TEST_URI=mongodb://localhost:27017/eventease_admin_test

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Security Configuration
BCRYPT_ROUNDS=12
ADMIN_REGISTRATION_CODE=EventEase2024!
SESSION_SECRET=your-session-secret-change-this-in-production

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=5

# Email Configuration (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=EventEase Admin

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Security Headers
HELMET_CONTENT_SECURITY_POLICY=true
HELMET_HSTS=true
HELMET_NO_SNIFF=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Cache Configuration
CACHE_TTL=300
REDIS_URL=redis://localhost:6379

# Two-Factor Authentication
TOTP_SERVICE_NAME=EventEase Admin
TOTP_ISSUER=EventEase

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Development Only
DEBUG=eventease:*
MOCK_EMAIL=true
