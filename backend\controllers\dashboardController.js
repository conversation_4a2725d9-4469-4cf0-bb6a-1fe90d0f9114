const User = require('../models/User');
const Vendor = require('../models/Vendor');
const mongoose = require('mongoose');

// Get dashboard statistics
const getDashboardStats = async (req, res) => {
  try {
    // Get basic counts
    const [
      totalUsers,
      totalVendors,
      activeVendors,
      pendingVendors,
      rejectedVendors,
      suspendedVendors
    ] = await Promise.all([
      User.countDocuments(),
      Vendor.countDocuments(),
      Vendor.countDocuments({ status: 'approved' }),
      Vendor.countDocuments({ status: 'pending' }),
      Vendor.countDocuments({ status: 'rejected' }),
      Vendor.countDocuments({ status: 'suspended' })
    ]);

    // Get user statistics by role
    const usersByRole = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get user statistics by status
    const usersByStatus = await User.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get vendor statistics by category
    const vendorsByCategory = await Vendor.aggregate([
      {
        $group: {
          _id: '$businessInfo.category',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [recentUsers, recentVendors] = await Promise.all([
      User.countDocuments({ createdAt: { $gte: thirtyDaysAgo } }),
      Vendor.countDocuments({ createdAt: { $gte: thirtyDaysAgo } })
    ]);

    // Get monthly growth data (last 12 months)
    const monthlyData = await getMonthlyGrowthData();

    // Get top rated vendors
    const topVendors = await Vendor.find({ status: 'approved' })
      .sort({ 'rating.average': -1, 'rating.count': -1 })
      .limit(5)
      .select('businessName rating businessInfo.category');

    // Get recent vendor applications
    const recentApplications = await Vendor.find({ status: 'pending' })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('businessName contactPerson email businessInfo.category createdAt');

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          totalVendors,
          activeVendors,
          pendingVendors,
          rejectedVendors,
          suspendedVendors,
          recentUsers,
          recentVendors
        },
        userStats: {
          byRole: usersByRole,
          byStatus: usersByStatus
        },
        vendorStats: {
          byCategory: vendorsByCategory,
          byStatus: [
            { _id: 'approved', count: activeVendors },
            { _id: 'pending', count: pendingVendors },
            { _id: 'rejected', count: rejectedVendors },
            { _id: 'suspended', count: suspendedVendors }
          ]
        },
        monthlyGrowth: monthlyData,
        topVendors,
        recentApplications
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: error.message
    });
  }
};

// Get monthly growth data for charts
const getMonthlyGrowthData = async () => {
  try {
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const userGrowth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: twelveMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    const vendorGrowth = await Vendor.aggregate([
      {
        $match: {
          createdAt: { $gte: twelveMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    return {
      users: userGrowth,
      vendors: vendorGrowth
    };
  } catch (error) {
    console.error('Monthly growth data error:', error);
    return { users: [], vendors: [] };
  }
};

// Get system health metrics
const getSystemHealth = async (req, res) => {
  try {
    // Database connection status
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';

    // Get database stats
    const dbStats = await mongoose.connection.db.stats();

    // Memory usage
    const memoryUsage = process.memoryUsage();

    // Uptime
    const uptime = process.uptime();

    // Recent errors (you might want to implement error logging)
    const recentErrors = []; // Placeholder for error logging system

    res.json({
      success: true,
      data: {
        database: {
          status: dbStatus,
          collections: dbStats.collections,
          dataSize: dbStats.dataSize,
          indexSize: dbStats.indexSize
        },
        server: {
          uptime: Math.floor(uptime),
          memoryUsage: {
            rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
            heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
            heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) // MB
          },
          nodeVersion: process.version,
          platform: process.platform
        },
        recentErrors,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('System health error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch system health metrics',
      error: error.message
    });
  }
};

// Get activity logs
const getActivityLogs = async (req, res) => {
  try {
    const { page = 1, limit = 20, type, dateFrom, dateTo } = req.query;

    // Build query
    let query = {};

    if (type) {
      query.type = type;
    }

    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    // For now, return mock data - you can implement actual activity logging
    const mockLogs = [
      {
        _id: '1',
        type: 'vendor_approval',
        message: 'Vendor "Elite Catering" was approved',
        user: 'Admin User',
        timestamp: new Date(),
        details: { vendorId: 'vendor123', action: 'approved' }
      },
      {
        _id: '2',
        type: 'user_registration',
        message: 'New user registered: <EMAIL>',
        user: 'System',
        timestamp: new Date(Date.now() - 3600000),
        details: { userId: 'user456', email: '<EMAIL>' }
      }
    ];

    res.json({
      success: true,
      data: {
        logs: mockLogs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: mockLogs.length,
          pages: Math.ceil(mockLogs.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Activity logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch activity logs',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardStats,
  getSystemHealth,
  getActivityLogs,
  getMonthlyGrowthData
};
