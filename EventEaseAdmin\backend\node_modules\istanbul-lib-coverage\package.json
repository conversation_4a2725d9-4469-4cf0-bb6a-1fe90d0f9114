{"name": "istanbul-lib-coverage", "version": "3.2.2", "description": "Data library for istanbul coverage objects", "author": "<PERSON><PERSON> <kananthm<PERSON>-<EMAIL>>", "main": "index.js", "files": ["lib", "index.js"], "scripts": {"test": "nyc mocha"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2"}, "karmaDeps": {"browserify-istanbul": "^0.2.1", "karma": "^0.13.10", "karma-browserify": "^4.2.1", "karma-chrome-launcher": "^0.2.0", "karma-coverage": "^0.4.2", "karma-mocha": "^0.2.0", "karma-phantomjs-launcher": "^0.2.0", "phantomjs": "^1.9.17"}, "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-coverage"}, "keywords": ["istanbul", "coverage", "data"], "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "engines": {"node": ">=8"}}