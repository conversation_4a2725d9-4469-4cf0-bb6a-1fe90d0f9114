const express = require('express');
const router = express.Router();
const { register, login, getProfile } = require('../controllers/authController');
const { protect } = require('../middleware/auth');

// Register a new admin
router.post('/register', register);

// Login admin
router.post('/login', login);

// Get admin profile (protected route)
router.get('/profile', protect, getProfile);

module.exports = router;
