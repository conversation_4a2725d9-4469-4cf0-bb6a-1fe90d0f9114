const express = require('express');
const router = express.Router();

// Mock security data
const mockSecurityLogs = [
  { id: 1, action: 'Login', user: '<EMAIL>', timestamp: '2024-05-26T10:30:00Z', ip: '***********' },
  { id: 2, action: 'Password Change', user: '<EMAIL>', timestamp: '2024-05-26T09:15:00Z', ip: '***********' },
  { id: 3, action: 'Failed Login', user: '<EMAIL>', timestamp: '2024-05-26T08:45:00Z', ip: '***********' }
];

// GET /api/security/logs - Get security logs
router.get('/logs', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockSecurityLogs,
      total: mockSecurityLogs.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch security logs',
      error: error.message
    });
  }
});

module.exports = router;
