const express = require('express');
const router = express.Router();

// Mock event data
const mockEvents = [
  { id: 1, name: 'Summer Music Festival', date: '2024-07-15', location: 'Central Park', status: 'active' },
  { id: 2, name: 'Tech Conference 2024', date: '2024-08-10', location: 'Convention Center', status: 'active' },
  { id: 3, name: 'Food & Wine Expo', date: '2024-09-05', location: 'Exhibition Hall', status: 'draft' }
];

// GET /api/events - Get all events
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockEvents,
      total: mockEvents.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch events',
      error: error.message
    });
  }
});

module.exports = router;
