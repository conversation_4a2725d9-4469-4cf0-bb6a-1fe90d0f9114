const express = require('express');
const router = express.Router();
const { 
  getVendors, 
  getVendor, 
  createVendor, 
  updateVendor, 
  deleteVendor 
} = require('../controllers/vendorController');
const { protect } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Get all vendors and create vendor
router.route('/')
  .get(getVendors)
  .post(createVendor);

// Get, update and delete single vendor
router.route('/:id')
  .get(getVendor)
  .put(updateVendor)
  .delete(deleteVendor);

module.exports = router;
