import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Modal,
  ScrollView,
  Alert,
  ActivityIndicator,
  Image
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const VendorManagementScreen = () => {
  // State for vendors data
  const [vendors, setVendors] = useState([]);
  const [filteredVendors, setFilteredVendors] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  // State for modal
  const [modalVisible, setModalVisible] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    businessName: '',
    category: 'restaurant',
    status: 'active',
    address: '',
    description: ''
  });

  // Load mock data
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const mockVendors = [
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '****** 567 890',
          businessName: 'Delicious Bites',
          category: 'restaurant',
          status: 'active',
          address: '123 Main St, New York, NY',
          description: 'Fine dining restaurant with Italian cuisine',
          joinedOn: '2023-01-10T08:15:00',
          rating: 4.8,
          totalOrders: 1245
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '****** 654 321',
          businessName: 'Morning Brew Cafe',
          category: 'cafe',
          status: 'active',
          address: '456 Park Ave, Boston, MA',
          description: 'Cozy cafe with specialty coffee and pastries',
          joinedOn: '2023-02-15T10:30:00',
          rating: 4.5,
          totalOrders: 890
        },
        {
          id: '3',
          name: 'Michael Brown',
          email: '<EMAIL>',
          phone: '****** 123 456',
          businessName: 'Sweet Treats Bakery',
          category: 'bakery',
          status: 'inactive',
          address: '789 Oak St, Chicago, IL',
          description: 'Artisan bakery specializing in cakes and pastries',
          joinedOn: '2023-01-05T16:45:00',
          rating: 4.2,
          totalOrders: 650
        },
        {
          id: '4',
          name: 'Emily Davis',
          email: '<EMAIL>',
          phone: '****** 789 012',
          businessName: 'Quick Bites',
          category: 'fast_food',
          status: 'active',
          address: '321 Pine St, Los Angeles, CA',
          description: 'Fast food restaurant with healthy options',
          joinedOn: '2023-03-20T09:15:00',
          rating: 4.0,
          totalOrders: 1500
        },
        {
          id: '5',
          name: 'David Wilson',
          email: '<EMAIL>',
          phone: '****** 456 789',
          businessName: 'Fresh Market',
          category: 'grocery',
          status: 'suspended',
          address: '654 Maple St, Seattle, WA',
          description: 'Organic grocery store with local produce',
          joinedOn: '2023-02-01T11:30:00',
          rating: 4.6,
          totalOrders: 780
        }
      ];
      
      setVendors(mockVendors);
      setFilteredVendors(mockVendors);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredVendors(vendors);
    } else {
      const filtered = vendors.filter(
        vendor => 
          vendor.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          vendor.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          vendor.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          vendor.phone.includes(searchQuery)
      );
      setFilteredVendors(filtered);
    }
  }, [searchQuery, vendors]);

  // Open modal for creating a new vendor
  const handleAddVendor = () => {
    setIsEditMode(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      businessName: '',
      category: 'restaurant',
      status: 'active',
      address: '',
      description: ''
    });
    setModalVisible(true);
  };

  // Open modal for editing an existing vendor
  const handleEditVendor = (vendor) => {
    setIsEditMode(true);
    setSelectedVendor(vendor);
    setFormData({
      name: vendor.name,
      email: vendor.email,
      phone: vendor.phone,
      businessName: vendor.businessName,
      category: vendor.category,
      status: vendor.status,
      address: vendor.address,
      description: vendor.description
    });
    setModalVisible(true);
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Save vendor (create or update)
  const handleSaveVendor = () => {
    // Validate form
    if (!formData.name || !formData.email || !formData.businessName) {
      Alert.alert('Error', 'Name, email, and business name are required');
      return;
    }

    if (isEditMode && selectedVendor) {
      // Update existing vendor
      const updatedVendors = vendors.map(vendor => 
        vendor.id === selectedVendor.id 
          ? { 
              ...vendor, 
              ...formData 
            } 
          : vendor
      );
      setVendors(updatedVendors);
      Alert.alert('Success', 'Vendor updated successfully');
    } else {
      // Create new vendor
      const newVendor = {
        id: Date.now().toString(),
        ...formData,
        joinedOn: new Date().toISOString(),
        rating: 0,
        totalOrders: 0
      };
      setVendors([...vendors, newVendor]);
      Alert.alert('Success', 'Vendor created successfully');
    }
    
    setModalVisible(false);
  };

  // Delete vendor
  const handleDeleteVendor = (vendorId) => {
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this vendor?',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Delete',
          onPress: () => {
            const updatedVendors = vendors.filter(vendor => vendor.id !== vendorId);
            setVendors(updatedVendors);
            Alert.alert('Success', 'Vendor deleted successfully');
          },
          style: 'destructive'
        }
      ]
    );
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get category label
  const getCategoryLabel = (category) => {
    const categories = {
      restaurant: 'Restaurant',
      cafe: 'Cafe',
      bakery: 'Bakery',
      fast_food: 'Fast Food',
      grocery: 'Grocery'
    };
    return categories[category] || category;
  };

  // Render vendor item
  const renderVendorItem = ({ item }) => (
    <View style={styles.vendorItem}>
      <View style={styles.vendorInfo}>
        <Text style={styles.businessName}>{item.businessName}</Text>
        <Text style={styles.vendorName}>{item.name}</Text>
        <Text style={styles.vendorEmail}>{item.email}</Text>
        <Text style={styles.vendorCategory}>{getCategoryLabel(item.category)}</Text>
      </View>
      
      <View style={styles.vendorMeta}>
        <View style={styles.vendorStatusContainer}>
          <Text style={[
            styles.vendorStatus,
            item.status === 'active' ? styles.statusActive :
            item.status === 'inactive' ? styles.statusInactive :
            styles.statusSuspended
          ]}>
            {item.status.toUpperCase()}
          </Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color="#FFC107" />
            <Text style={styles.ratingText}>{item.rating.toFixed(1)}</Text>
          </View>
        </View>
        
        <View style={styles.vendorActions}>
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => handleEditVendor(item)}
          >
            <Ionicons name="create-outline" size={20} color="#2196F3" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={() => handleDeleteVendor(item.id)}
          >
            <Ionicons name="trash-outline" size={20} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Vendor Management</Text>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={handleAddVendor}
        >
          <Ionicons name="add" size={20} color="#fff" />
          <Text style={styles.addButtonText}>Add Vendor</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.searchContainer}>
        <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search vendors by name, business, or email"
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Ionicons name="close-circle" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>
      
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Loading vendors...</Text>
        </View>
      ) : (
        <>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{vendors.length}</Text>
              <Text style={styles.statLabel}>Total Vendors</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {vendors.filter(vendor => vendor.status === 'active').length}
              </Text>
              <Text style={styles.statLabel}>Active</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>
                {vendors.reduce((total, vendor) => total + vendor.totalOrders, 0)}
              </Text>
              <Text style={styles.statLabel}>Total Orders</Text>
            </View>
          </View>
          
          <FlatList
            data={filteredVendors}
            renderItem={renderVendorItem}
            keyExtractor={item => item.id}
            style={styles.vendorList}
            contentContainerStyle={styles.vendorListContent}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="business" size={50} color="#ccc" />
                <Text style={styles.emptyText}>No vendors found</Text>
              </View>
            }
          />
        </>
      )}
      
      {/* Vendor Form Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {isEditMode ? 'Edit Vendor' : 'Add New Vendor'}
              </Text>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Business Name</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.businessName}
                  onChangeText={(text) => handleInputChange('businessName', text)}
                  placeholder="Enter business name"
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Owner Name</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.name}
                  onChangeText={(text) => handleInputChange('name', text)}
                  placeholder="Enter owner name"
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Email</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.email}
                  onChangeText={(text) => handleInputChange('email', text)}
                  placeholder="Enter email address"
                  keyboardType="email-address"
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Phone</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.phone}
                  onChangeText={(text) => handleInputChange('phone', text)}
                  placeholder="Enter phone number"
                  keyboardType="phone-pad"
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Address</Text>
                <TextInput
                  style={styles.formInput}
                  value={formData.address}
                  onChangeText={(text) => handleInputChange('address', text)}
                  placeholder="Enter business address"
                  multiline
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Description</Text>
                <TextInput
                  style={[styles.formInput, styles.textArea]}
                  value={formData.description}
                  onChangeText={(text) => handleInputChange('description', text)}
                  placeholder="Enter business description"
                  multiline
                  numberOfLines={4}
                />
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Category</Text>
                <View style={styles.radioGroup}>
                  {['restaurant', 'cafe', 'bakery', 'fast_food', 'grocery'].map((category) => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.radioButton,
                        formData.category === category && styles.radioButtonSelected
                      ]}
                      onPress={() => handleInputChange('category', category)}
                    >
                      <Text style={styles.radioText}>{getCategoryLabel(category)}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Status</Text>
                <View style={styles.radioGroup}>
                  <TouchableOpacity
                    style={[
                      styles.radioButton,
                      formData.status === 'active' && styles.radioButtonSelected
                    ]}
                    onPress={() => handleInputChange('status', 'active')}
                  >
                    <Text style={styles.radioText}>Active</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.radioButton,
                      formData.status === 'inactive' && styles.radioButtonSelected
                    ]}
                    onPress={() => handleInputChange('status', 'inactive')}
                  >
                    <Text style={styles.radioText}>Inactive</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[
                      styles.radioButton,
                      formData.status === 'suspended' && styles.radioButtonSelected
                    ]}
                    onPress={() => handleInputChange('status', 'suspended')}
                  >
                    <Text style={styles.radioText}>Suspended</Text>
                  </TouchableOpacity>
                </View>
              </View>
              
              {isEditMode && selectedVendor && (
                <View style={styles.vendorDetails}>
                  <Text style={styles.detailsTitle}>Vendor Details</Text>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Joined On:</Text>
                    <Text style={styles.detailValue}>
                      {formatDate(selectedVendor.joinedOn)}
                    </Text>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Rating:</Text>
                    <View style={styles.detailRating}>
                      <Text style={styles.detailValue}>{selectedVendor.rating.toFixed(1)}</Text>
                      <Ionicons name="star" size={16} color="#FFC107" style={styles.detailRatingIcon} />
                    </View>
                  </View>
                  
                  <View style={styles.detailItem}>
                    <Text style={styles.detailLabel}>Total Orders:</Text>
                    <Text style={styles.detailValue}>{selectedVendor.totalOrders}</Text>
                  </View>
                </View>
              )}
            </ScrollView>
            
            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.cancelButton}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.saveButton}
                onPress={handleSaveVendor}
              >
                <Text style={styles.saveButtonText}>
                  {isEditMode ? 'Update Vendor' : 'Create Vendor'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 5,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 20,
    height: 50,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  statItem: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 15,
    marginRight: 10,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
  vendorList: {
    flex: 1,
  },
  vendorListContent: {
    paddingBottom: 20,
  },
  vendorItem: {
    backgroundColor: '#fff',
    borderRadius: 5,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  vendorInfo: {
    flex: 2,
  },
  businessName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  vendorName: {
    fontSize: 16,
    color: '#666',
    marginBottom: 5,
  },
  vendorEmail: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  vendorCategory: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
  },
  vendorMeta: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  vendorStatusContainer: {
    alignItems: 'flex-end',
  },
  vendorStatus: {
    fontSize: 12,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
    marginBottom: 5,
  },
  statusActive: {
    backgroundColor: '#e8f5e9',
    color: '#4CAF50',
  },
  statusInactive: {
    backgroundColor: '#f5f5f5',
    color: '#9e9e9e',
  },
  statusSuspended: {
    backgroundColor: '#ffebee',
    color: '#f44336',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#666',
    marginLeft: 5,
  },
  vendorActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: 5,
    marginLeft: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 50,
  },
  emptyText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    width: '90%',
    maxWidth: 500,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalBody: {
    padding: 15,
    maxHeight: 400,
  },
  formGroup: {
    marginBottom: 15,
  },
  formLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 5,
    fontWeight: '500',
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 5,
    padding: 10,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  radioGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  radioButton: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 5,
    padding: 10,
    marginRight: 10,
    marginBottom: 10,
  },
  radioButtonSelected: {
    backgroundColor: '#e3f2fd',
    borderColor: '#2196F3',
  },
  radioText: {
    fontSize: 14,
    color: '#333',
  },
  vendorDetails: {
    marginTop: 20,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 5,
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    width: 120,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  detailRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailRatingIcon: {
    marginLeft: 5,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  cancelButton: {
    padding: 10,
    marginRight: 10,
  },
  cancelButtonText: {
    color: '#666',
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: '#2196F3',
    padding: 10,
    borderRadius: 5,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default VendorManagementScreen;
