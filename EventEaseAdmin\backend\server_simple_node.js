const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = 3001;

// Mock data
const mockUsers = [
  {
    _id: '1',
    firstName: 'Super',
    lastName: 'Admin',
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    role: 'super_admin',
    status: 'active'
  },
  {
    _id: '2',
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active'
  },
  {
    _id: '3',
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    role: 'user',
    status: 'active'
  }
];

const mockVendors = [
  {
    _id: '1',
    businessName: 'Elite Catering',
    email: '<EMAIL>',
    status: 'pending',
    businessInfo: { category: 'Catering' }
  },
  {
    _id: '2',
    businessName: 'Perfect Photos',
    email: '<EMAIL>',
    status: 'approved',
    businessInfo: { category: 'Photography' }
  },
  {
    _id: '3',
    businessName: 'Dream Decorations',
    email: '<EMAIL>',
    status: 'pending',
    businessInfo: { category: 'Decoration' }
  }
];

const mockStats = {
  overview: {
    totalUsers: 1247,
    totalVendors: 89,
    totalBookings: 456,
    totalEvents: 123,
    totalRevenue: 89750,
    activeUsers: 1156,
    pendingVendors: 12
  }
};

// Helper function to parse JSON body
function parseBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const parsed = body ? JSON.parse(body) : {};
      callback(null, parsed);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Helper function to send JSON response
function sendJSON(res, statusCode, data) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Create HTTP server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  // Handle CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Health check
  if (path === '/api/health' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'EventEase Admin Backend is running',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // Auth login
  if (path === '/api/auth/login' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendJSON(res, 400, { success: false, message: 'Invalid JSON' });
        return;
      }

      const { email, password } = body;
      const user = mockUsers.find(u => u.email === email && u.password === password);
      
      if (!user) {
        sendJSON(res, 401, {
          success: false,
          message: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS'
        });
        return;
      }

      const { password: _, ...userWithoutPassword } = user;
      sendJSON(res, 200, {
        success: true,
        message: 'Login successful',
        data: {
          user: userWithoutPassword,
          token: 'mock-token-' + user._id
        }
      });
    });
    return;
  }

  // Auth register
  if (path === '/api/auth/register' && method === 'POST') {
    parseBody(req, (err, body) => {
      if (err) {
        sendJSON(res, 400, { success: false, message: 'Invalid JSON' });
        return;
      }

      const { firstName, lastName, email, password, adminCode } = body;

      if (adminCode !== 'ADMIN2024') {
        sendJSON(res, 400, {
          success: false,
          message: 'Invalid admin registration code',
          code: 'INVALID_ADMIN_CODE'
        });
        return;
      }

      const existingUser = mockUsers.find(u => u.email === email);
      if (existingUser) {
        sendJSON(res, 400, {
          success: false,
          message: 'Email already exists',
          code: 'EMAIL_EXISTS'
        });
        return;
      }

      const newUser = {
        _id: String(mockUsers.length + 1),
        firstName,
        lastName,
        email,
        password,
        role: 'admin',
        status: 'active'
      };

      mockUsers.push(newUser);
      const { password: _, ...userWithoutPassword } = newUser;

      sendJSON(res, 200, {
        success: true,
        message: 'Registration successful',
        data: {
          user: userWithoutPassword,
          token: 'mock-token-' + newUser._id
        }
      });
    });
    return;
  }

  // Dashboard stats
  if (path === '/api/dashboard/stats' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Dashboard statistics retrieved successfully',
      data: mockStats
    });
    return;
  }

  // Users
  if (path === '/api/users' && method === 'GET') {
    const usersWithoutPasswords = mockUsers.map(({ password, ...user }) => user);
    sendJSON(res, 200, {
      success: true,
      message: 'Users retrieved successfully',
      data: {
        users: usersWithoutPasswords,
        pagination: {
          page: 1,
          limit: 10,
          total: mockUsers.length,
          totalPages: 1
        }
      }
    });
    return;
  }

  // Vendors
  if (path === '/api/vendors' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Vendors retrieved successfully',
      data: {
        vendors: mockVendors,
        pagination: {
          page: 1,
          limit: 10,
          total: mockVendors.length,
          totalPages: 1
        }
      }
    });
    return;
  }

  // Events
  if (path === '/api/events' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Events endpoint - Coming soon',
      data: {
        events: [],
        message: 'Events management functionality will be implemented here'
      }
    });
    return;
  }

  // Bookings
  if (path === '/api/bookings' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Bookings endpoint - Coming soon',
      data: {
        bookings: [],
        message: 'Bookings management functionality will be implemented here'
      }
    });
    return;
  }

  // Analytics
  if (path === '/api/analytics' && method === 'GET') {
    sendJSON(res, 200, {
      success: true,
      message: 'Analytics endpoint - Coming soon',
      data: {
        analytics: {},
        message: 'Advanced analytics functionality will be implemented here'
      }
    });
    return;
  }

  // 404 handler
  sendJSON(res, 404, {
    success: false,
    message: 'API endpoint not found',
    path: path
  });
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 EventEase Admin Backend running on port ${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log('📧 Default admin: <EMAIL>');
  console.log('🔑 Default password: SuperAdmin123!');
  console.log('✅ Backend server is ready!');
});

// Handle server errors
server.on('error', (err) => {
  console.error('❌ Server error:', err.message);
});

module.exports = server;
