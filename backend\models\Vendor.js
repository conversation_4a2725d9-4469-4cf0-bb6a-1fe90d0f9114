const mongoose = require('mongoose');

const vendorSchema = new mongoose.Schema({
  businessName: {
    type: String,
    required: [true, 'Business name is required'],
    trim: true,
    maxlength: [100, 'Business name cannot exceed 100 characters']
  },
  contactPerson: {
    firstName: {
      type: String,
      required: [true, 'Contact person first name is required'],
      trim: true
    },
    lastName: {
      type: String,
      required: [true, 'Contact person last name is required'],
      trim: true
    },
    position: {
      type: String,
      trim: true
    }
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    primary: {
      type: String,
      required: [true, 'Primary phone is required'],
      trim: true
    },
    secondary: {
      type: String,
      trim: true
    }
  },
  address: {
    street: {
      type: String,
      required: [true, 'Street address is required']
    },
    city: {
      type: String,
      required: [true, 'City is required']
    },
    state: {
      type: String,
      required: [true, 'State is required']
    },
    zipCode: {
      type: String,
      required: [true, 'Zip code is required']
    },
    country: {
      type: String,
      required: [true, 'Country is required'],
      default: 'United States'
    }
  },
  businessInfo: {
    category: {
      type: String,
      required: [true, 'Business category is required'],
      enum: [
        'Catering',
        'Photography',
        'Videography',
        'Music & Entertainment',
        'Decoration & Flowers',
        'Venue',
        'Transportation',
        'Audio/Visual',
        'Event Planning',
        'Security',
        'Cleaning',
        'Other'
      ]
    },
    subCategory: String,
    description: {
      type: String,
      required: [true, 'Business description is required'],
      maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    website: {
      type: String,
      match: [/^https?:\/\/.+/, 'Please enter a valid website URL']
    },
    socialMedia: {
      facebook: String,
      instagram: String,
      twitter: String,
      linkedin: String
    },
    yearsInBusiness: {
      type: Number,
      min: [0, 'Years in business cannot be negative']
    },
    employeeCount: {
      type: String,
      enum: ['1-5', '6-20', '21-50', '51-100', '100+']
    }
  },
  services: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    price: {
      min: Number,
      max: Number,
      currency: {
        type: String,
        default: 'USD'
      }
    },
    duration: String,
    availability: {
      type: String,
      enum: ['Available', 'Limited', 'Unavailable'],
      default: 'Available'
    }
  }],
  documents: {
    businessLicense: {
      filename: String,
      url: String,
      uploadedAt: Date,
      verified: {
        type: Boolean,
        default: false
      }
    },
    insurance: {
      filename: String,
      url: String,
      uploadedAt: Date,
      verified: {
        type: Boolean,
        default: false
      }
    },
    certifications: [{
      name: String,
      filename: String,
      url: String,
      uploadedAt: Date,
      verified: {
        type: Boolean,
        default: false
      }
    }]
  },
  portfolio: [{
    title: String,
    description: String,
    images: [String],
    eventDate: Date,
    eventType: String
  }],
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'suspended', 'under_review'],
    default: 'pending'
  },
  approvalInfo: {
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectedAt: Date,
    rejectionReason: String,
    notes: String
  },
  rating: {
    average: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    count: {
      type: Number,
      default: 0
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['basic', 'premium', 'enterprise'],
      default: 'basic'
    },
    startDate: Date,
    endDate: Date,
    isActive: {
      type: Boolean,
      default: true
    }
  },
  settings: {
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    },
    visibility: {
      type: String,
      enum: ['public', 'private'],
      default: 'public'
    }
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for contact person full name
vendorSchema.virtual('contactPersonName').get(function() {
  return `${this.contactPerson.firstName} ${this.contactPerson.lastName}`;
});

// Virtual for full address
vendorSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  return `${addr.street}, ${addr.city}, ${addr.state} ${addr.zipCode}, ${addr.country}`;
});

// Static method for vendor statistics
vendorSchema.statics.getStatistics = async function() {
  const stats = await this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);
  
  const categoryStats = await this.aggregate([
    {
      $group: {
        _id: '$businessInfo.category',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } }
  ]);
  
  return { statusStats: stats, categoryStats };
};

// Index for better performance
vendorSchema.index({ email: 1 });
vendorSchema.index({ status: 1 });
vendorSchema.index({ 'businessInfo.category': 1 });
vendorSchema.index({ createdAt: -1 });
vendorSchema.index({ 'rating.average': -1 });

module.exports = mongoose.model('Vendor', vendorSchema);
