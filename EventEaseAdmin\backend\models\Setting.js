const mongoose = require('mongoose');

const settingSchema = new mongoose.Schema({
    key: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    value: {
        type: mongoose.Schema.Types.Mixed,
        required: true
    },
    defaultValue: {
        type: mongoose.Schema.Types.Mixed,
        required: true
    },
    category: {
        type: String,
        required: true,
        enum: [
            'general',
            'security',
            'email',
            'notifications',
            'appearance',
            'integrations',
            'backup',
            'performance',
            'analytics',
            'maintenance'
        ]
    },
    type: {
        type: String,
        required: true,
        enum: ['string', 'number', 'boolean', 'array', 'object', 'json']
    },
    description: {
        type: String,
        required: true
    },
    isPublic: {
        type: Boolean,
        default: false // Whether this setting can be accessed by non-admin users
    },
    isEditable: {
        type: Boolean,
        default: true // Whether this setting can be modified
    },
    validation: {
        required: {
            type: Boolean,
            default: false
        },
        min: Number,
        max: Number,
        pattern: String,
        options: [String] // For enum-like settings
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
}, {
    timestamps: true
});

// Indexes
settingSchema.index({ category: 1, key: 1 });
settingSchema.index({ key: 1 }, { unique: true });

// Virtual for display name
settingSchema.virtual('displayName').get(function() {
    return this.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
});

// Method to validate setting value
settingSchema.methods.validateValue = function(value) {
    const errors = [];

    // Type validation
    switch (this.type) {
        case 'string':
            if (typeof value !== 'string') {
                errors.push('Value must be a string');
            }
            break;
        case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
                errors.push('Value must be a number');
            }
            break;
        case 'boolean':
            if (typeof value !== 'boolean') {
                errors.push('Value must be a boolean');
            }
            break;
        case 'array':
            if (!Array.isArray(value)) {
                errors.push('Value must be an array');
            }
            break;
        case 'object':
        case 'json':
            if (typeof value !== 'object' || value === null) {
                errors.push('Value must be an object');
            }
            break;
    }

    // Validation rules
    if (this.validation) {
        if (this.validation.required && (value === null || value === undefined || value === '')) {
            errors.push('Value is required');
        }

        if (this.type === 'number' && typeof value === 'number') {
            if (this.validation.min !== undefined && value < this.validation.min) {
                errors.push(`Value must be at least ${this.validation.min}`);
            }
            if (this.validation.max !== undefined && value > this.validation.max) {
                errors.push(`Value must be at most ${this.validation.max}`);
            }
        }

        if (this.type === 'string' && typeof value === 'string') {
            if (this.validation.pattern) {
                const regex = new RegExp(this.validation.pattern);
                if (!regex.test(value)) {
                    errors.push('Value does not match required pattern');
                }
            }
            if (this.validation.min !== undefined && value.length < this.validation.min) {
                errors.push(`Value must be at least ${this.validation.min} characters`);
            }
            if (this.validation.max !== undefined && value.length > this.validation.max) {
                errors.push(`Value must be at most ${this.validation.max} characters`);
            }
        }

        if (this.validation.options && this.validation.options.length > 0) {
            if (!this.validation.options.includes(value)) {
                errors.push(`Value must be one of: ${this.validation.options.join(', ')}`);
            }
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

// Static method to get setting value by key
settingSchema.statics.getValue = async function(key, defaultValue = null) {
    try {
        const setting = await this.findOne({ key });
        return setting ? setting.value : defaultValue;
    } catch (error) {
        console.error(`Error getting setting ${key}:`, error);
        return defaultValue;
    }
};

// Static method to set setting value by key
settingSchema.statics.setValue = async function(key, value, userId) {
    try {
        const setting = await this.findOne({ key });
        if (setting) {
            const validation = setting.validateValue(value);
            if (!validation.isValid) {
                throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
            }
            
            setting.value = value;
            setting.updatedBy = userId;
            await setting.save();
            return setting;
        } else {
            throw new Error(`Setting ${key} not found`);
        }
    } catch (error) {
        console.error(`Error setting ${key}:`, error);
        throw error;
    }
};

// Static method to initialize default settings
settingSchema.statics.initializeDefaults = async function(userId) {
    const defaultSettings = [
        // General Settings
        {
            key: 'site_name',
            value: 'EventEase Admin',
            defaultValue: 'EventEase Admin',
            category: 'general',
            type: 'string',
            description: 'The name of the application',
            isPublic: true,
            validation: { required: true, min: 1, max: 100 }
        },
        {
            key: 'site_description',
            value: 'Professional Event Management Dashboard',
            defaultValue: 'Professional Event Management Dashboard',
            category: 'general',
            type: 'string',
            description: 'Description of the application',
            isPublic: true,
            validation: { max: 500 }
        },
        {
            key: 'timezone',
            value: 'UTC',
            defaultValue: 'UTC',
            category: 'general',
            type: 'string',
            description: 'Default timezone for the application',
            validation: { required: true }
        },
        {
            key: 'date_format',
            value: 'YYYY-MM-DD',
            defaultValue: 'YYYY-MM-DD',
            category: 'general',
            type: 'string',
            description: 'Default date format',
            validation: { 
                required: true,
                options: ['YYYY-MM-DD', 'DD/MM/YYYY', 'MM/DD/YYYY', 'DD-MM-YYYY']
            }
        },

        // Security Settings
        {
            key: 'session_timeout',
            value: 3600,
            defaultValue: 3600,
            category: 'security',
            type: 'number',
            description: 'Session timeout in seconds',
            validation: { required: true, min: 300, max: 86400 }
        },
        {
            key: 'password_min_length',
            value: 8,
            defaultValue: 8,
            category: 'security',
            type: 'number',
            description: 'Minimum password length',
            validation: { required: true, min: 6, max: 128 }
        },
        {
            key: 'require_2fa',
            value: false,
            defaultValue: false,
            category: 'security',
            type: 'boolean',
            description: 'Require two-factor authentication for all users'
        },
        {
            key: 'max_login_attempts',
            value: 5,
            defaultValue: 5,
            category: 'security',
            type: 'number',
            description: 'Maximum login attempts before account lockout',
            validation: { required: true, min: 3, max: 10 }
        },
        {
            key: 'account_lockout_duration',
            value: 900,
            defaultValue: 900,
            category: 'security',
            type: 'number',
            description: 'Account lockout duration in seconds',
            validation: { required: true, min: 300, max: 3600 }
        },

        // Email Settings
        {
            key: 'smtp_host',
            value: '',
            defaultValue: '',
            category: 'email',
            type: 'string',
            description: 'SMTP server hostname'
        },
        {
            key: 'smtp_port',
            value: 587,
            defaultValue: 587,
            category: 'email',
            type: 'number',
            description: 'SMTP server port',
            validation: { min: 1, max: 65535 }
        },
        {
            key: 'smtp_secure',
            value: true,
            defaultValue: true,
            category: 'email',
            type: 'boolean',
            description: 'Use secure SMTP connection'
        },
        {
            key: 'email_from_address',
            value: '<EMAIL>',
            defaultValue: '<EMAIL>',
            category: 'email',
            type: 'string',
            description: 'Default from email address',
            validation: { pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' }
        },

        // Notification Settings
        {
            key: 'enable_email_notifications',
            value: true,
            defaultValue: true,
            category: 'notifications',
            type: 'boolean',
            description: 'Enable email notifications'
        },
        {
            key: 'enable_push_notifications',
            value: true,
            defaultValue: true,
            category: 'notifications',
            type: 'boolean',
            description: 'Enable push notifications'
        },

        // Performance Settings
        {
            key: 'api_rate_limit',
            value: 100,
            defaultValue: 100,
            category: 'performance',
            type: 'number',
            description: 'API rate limit per 15 minutes',
            validation: { required: true, min: 10, max: 1000 }
        },
        {
            key: 'cache_duration',
            value: 300,
            defaultValue: 300,
            category: 'performance',
            type: 'number',
            description: 'Cache duration in seconds',
            validation: { required: true, min: 60, max: 3600 }
        }
    ];

    for (const settingData of defaultSettings) {
        const existingSetting = await this.findOne({ key: settingData.key });
        if (!existingSetting) {
            await this.create({
                ...settingData,
                createdBy: userId,
                updatedBy: userId
            });
        }
    }
};

module.exports = mongoose.model('Setting', settingSchema);
