import axios from 'axios';

const API_URL = 'http://localhost:5000/api/vendors';

// Get auth token from localStorage
const getToken = () => {
  return localStorage.getItem('adminToken');
};

// Configure axios with auth token
const authAxios = () => {
  const token = getToken();
  
  return axios.create({
    baseURL: API_URL,
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
};

// Get all vendors
export const getVendors = async () => {
  try {
    const response = await authAxios().get('/');
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Get single vendor
export const getVendor = async (vendorId) => {
  try {
    const response = await authAxios().get(`/${vendorId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Create vendor
export const createVendor = async (vendorData) => {
  try {
    const response = await authAxios().post('/', vendorData);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Update vendor
export const updateVendor = async (vendorId, vendorData) => {
  try {
    const response = await authAxios().put(`/${vendorId}`, vendorData);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Delete vendor
export const deleteVendor = async (vendorId) => {
  try {
    const response = await authAxios().delete(`/${vendorId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};
