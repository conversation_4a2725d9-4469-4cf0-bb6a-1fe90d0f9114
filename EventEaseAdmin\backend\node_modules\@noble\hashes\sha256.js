"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sha224 = exports.SHA224 = exports.sha256 = exports.SHA256 = void 0;
/**
 * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.
 *
 * To break sha256 using birthday attack, attackers need to try 2^128 hashes.
 * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.
 *
 * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).
 * @module
 * @deprecated
 */
const sha2_ts_1 = require("./sha2.js");
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA256 = sha2_ts_1.SHA256;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha256 = sha2_ts_1.sha256;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.SHA224 = sha2_ts_1.SHA224;
/** @deprecated Use import from `noble/hashes/sha2` module */
exports.sha224 = sha2_ts_1.sha224;
//# sourceMappingURL=sha256.js.map