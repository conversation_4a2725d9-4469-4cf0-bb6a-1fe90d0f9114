const express = require('express');
const router = express.Router();
const {
  getAllVendors,
  getVendorById,
  createVendor,
  updateVendor,
  approveVendor,
  rejectVendor,
  suspendVendor,
  getVendorStats
} = require('../controllers/vendorController');

// @route   GET /api/vendors
// @desc    Get all vendors with pagination and filtering
// @access  Private (Admin)
router.get('/', getAllVendors);

// @route   GET /api/vendors/stats
// @desc    Get vendor statistics
// @access  Private (Admin)
router.get('/stats', getVendorStats);

// @route   GET /api/vendors/:id
// @desc    Get vendor by ID
// @access  Private (Admin)
router.get('/:id', getVendorById);

// @route   POST /api/vendors
// @desc    Create new vendor application
// @access  Public
router.post('/', createVendor);

// @route   PUT /api/vendors/:id
// @desc    Update vendor information
// @access  Private (Admin/Vendor)
router.put('/:id', updateVendor);

// @route   PUT /api/vendors/:id/approve
// @desc    Approve vendor application
// @access  Private (Admin)
router.put('/:id/approve', approveVendor);

// @route   PUT /api/vendors/:id/reject
// @desc    Reject vendor application
// @access  Private (Admin)
router.put('/:id/reject', rejectVendor);

// @route   PUT /api/vendors/:id/suspend
// @desc    Suspend approved vendor
// @access  Private (Admin)
router.put('/:id/suspend', suspendVendor);

module.exports = router;
