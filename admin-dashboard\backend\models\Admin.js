const bcrypt = require('bcryptjs');
const db = require('../mockDb');

// Admin model for mock database
class Admin {
  constructor(data) {
    this._id = data._id || Date.now().toString();
    this.username = data.username;
    this.email = data.email;
    this.password = data.password;
    this.role = data.role || 'admin';
    this.createdAt = data.createdAt || new Date();
  }

  // Save admin to database
  async save() {
    // Check if admin already exists
    const existingAdmin = db.admins.find(
      admin => admin.email === this.email || admin.username === this.username
    );

    if (existingAdmin) {
      throw new Error('Admin already exists');
    }

    // Hash password if not already hashed
    if (!this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }

    // Add to database
    db.admins.push(this);
    return this;
  }

  // Compare password
  async comparePassword(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
  }

  // Find admin by ID
  static async findById(id) {
    const admin = db.admins.find(admin => admin._id === id);

    if (!admin) {
      return null;
    }

    return new Admin(admin);
  }

  // Find admin by email
  static async findOne(query) {
    let admin;

    if (query.email) {
      admin = db.admins.find(admin => admin.email === query.email);
    } else if (query.$or) {
      admin = db.admins.find(
        admin =>
          query.$or.some(
            condition =>
              (condition.email && condition.email === admin.email) ||
              (condition.username && condition.username === admin.username)
          )
      );
    }

    if (!admin) {
      return null;
    }

    return new Admin(admin);
  }

  // Select fields to return
  select(fields) {
    if (fields === '-password') {
      const { password, ...adminWithoutPassword } = this;
      return adminWithoutPassword;
    }
    return this;
  }
}

module.exports = Admin;
