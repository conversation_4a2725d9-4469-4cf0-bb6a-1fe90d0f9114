'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
exports.default = void 0;
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

const config = {
  comment: '  A comment',
  condition: () => true,
  deprecate: () => false,
  deprecatedConfig: {
    key: () => 'Deprecation message'
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  error: () => {},
  exampleConfig: {
    key: 'value',
    test: 'case'
  },
  recursive: true,
  recursiveDenylist: [],
  title: {
    deprecation: 'Deprecation Warning',
    error: 'Validation Error',
    warning: 'Validation Warning'
  },
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  unknown: () => {}
};
var _default = config;
exports.default = _default;
