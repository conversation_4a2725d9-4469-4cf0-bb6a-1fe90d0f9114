"use strict";

/**
 * Module dependencies.
 */

const util = require('util');
const Stream = require('stream');
const ResponseBase = require('../response-base');
const _require = require('../utils'),
  mixin = _require.mixin;

/**
 * Expose `Response`.
 */

module.exports = Response;

/**
 * Initialize a new `Response` with the given `xhr`.
 *
 *  - set flags (.ok, .error, etc)
 *  - parse header
 *
 * @param {Request} req
 * @param {Object} options
 * @constructor
 * @extends {Stream}
 * @implements {ReadableStream}
 * @api private
 */

function Response(request) {
  Stream.call(this);
  this.res = request.res;
  const res = this.res;
  this.request = request;
  this.req = request.req;
  this.text = res.text;
  this.files = res.files || {};
  this.buffered = request._resBuffered;
  this.headers = res.headers;
  this.header = this.headers;
  this._setStatusProperties(res.statusCode);
  this._setHeaderProperties(this.header);
  this.setEncoding = res.setEncoding.bind(res);
  res.on('data', this.emit.bind(this, 'data'));
  res.on('end', this.emit.bind(this, 'end'));
  res.on('close', this.emit.bind(this, 'close'));
  res.on('error', this.emit.bind(this, 'error'));
}

// Lazy access res.body.
// https://github.com/nodejs/node/pull/39520#issuecomment-889697136
Object.defineProperty(Response.prototype, 'body', {
  get() {
    return this._body !== undefined ? this._body : this.res.body !== undefined ? this.res.body : {};
  },
  set(value) {
    this._body = value;
  }
});

/**
 * Inherit from `Stream`.
 */

util.inherits(Response, Stream);
mixin(Response.prototype, ResponseBase.prototype);

/**
 * Implements methods of a `ReadableStream`
 */

Response.prototype.destroy = function (error) {
  this.res.destroy(error);
};

/**
 * Pause.
 */

Response.prototype.pause = function () {
  this.res.pause();
};

/**
 * Resume.
 */

Response.prototype.resume = function () {
  this.res.resume();
};

/**
 * Return an `Error` representative of this response.
 *
 * @return {Error}
 * @api public
 */

Response.prototype.toError = function () {
  const req = this.req;
  const method = req.method;
  const path = req.path;
  const message = `cannot ${method} ${path} (${this.status})`;
  const error = new Error(message);
  error.status = this.status;
  error.text = this.text;
  error.method = method;
  error.path = path;
  return error;
};
Response.prototype.setStatusProperties = function (status) {
  console.warn('In superagent 2.x setStatusProperties is a private method');
  return this._setStatusProperties(status);
};

/**
 * To json.
 *
 * @return {Object}
 * @api public
 */

Response.prototype.toJSON = function () {
  return {
    req: this.request.toJSON(),
    header: this.header,
    status: this.status,
    text: this.text
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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