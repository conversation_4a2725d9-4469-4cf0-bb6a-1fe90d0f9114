const mongoose = require('mongoose');
const User = require('../models/User');
const Vendor = require('../models/Vendor');
require('dotenv').config();

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/eventease_admin';

const seedUsers = [
  {
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    status: 'active',
    phone: '******-0101',
    emailVerified: true,
    address: {
      street: '123 Admin Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'United States'
    }
  },
  {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    status: 'active',
    phone: '******-0102',
    emailVerified: true,
    address: {
      street: '456 User Avenue',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90001',
      country: 'United States'
    }
  },
  {
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    status: 'active',
    phone: '******-0103',
    emailVerified: true
  },
  {
    firstName: 'Bob',
    lastName: 'Johnson',
    email: '<EMAIL>',
    password: 'password123',
    role: 'moderator',
    status: 'active',
    phone: '******-0104',
    emailVerified: false
  },
  {
    firstName: 'Alice',
    lastName: 'Williams',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    status: 'inactive',
    phone: '******-0105'
  }
];

const seedVendors = [
  {
    businessName: 'Elite Event Catering',
    contactPerson: {
      firstName: 'Michael',
      lastName: 'Chen',
      position: 'Owner'
    },
    email: '<EMAIL>',
    phone: {
      primary: '******-0201',
      secondary: '******-0202'
    },
    address: {
      street: '789 Catering Blvd',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      country: 'United States'
    },
    businessInfo: {
      category: 'Catering',
      description: 'Premium catering services for corporate events, weddings, and special occasions. We specialize in gourmet cuisine with international flavors.',
      website: 'https://elitecatering.com',
      yearsInBusiness: 8,
      employeeCount: '21-50'
    },
    services: [
      {
        name: 'Corporate Catering',
        description: 'Professional catering for business events',
        price: { min: 25, max: 75, currency: 'USD' },
        duration: 'Per person',
        availability: 'Available'
      },
      {
        name: 'Wedding Catering',
        description: 'Full-service wedding catering packages',
        price: { min: 50, max: 150, currency: 'USD' },
        duration: 'Per person',
        availability: 'Available'
      }
    ],
    status: 'pending'
  },
  {
    businessName: 'SoundWave Audio Solutions',
    contactPerson: {
      firstName: 'Sarah',
      lastName: 'Davis',
      position: 'Technical Director'
    },
    email: '<EMAIL>',
    phone: {
      primary: '******-0301'
    },
    address: {
      street: '456 Audio Street',
      city: 'Nashville',
      state: 'TN',
      zipCode: '37201',
      country: 'United States'
    },
    businessInfo: {
      category: 'Audio/Visual',
      description: 'Professional audio and visual equipment rental and setup for events of all sizes.',
      website: 'https://soundwaveaudio.com',
      yearsInBusiness: 12,
      employeeCount: '6-20'
    },
    services: [
      {
        name: 'Sound System Rental',
        description: 'Professional PA systems and microphones',
        price: { min: 200, max: 1500, currency: 'USD' },
        duration: 'Per day',
        availability: 'Available'
      }
    ],
    status: 'approved',
    approvalInfo: {
      approvedAt: new Date('2024-01-15'),
      notes: 'Excellent references and equipment quality'
    },
    rating: {
      average: 4.8,
      count: 24
    }
  },
  {
    businessName: 'Floral Elegance Designs',
    contactPerson: {
      firstName: 'Emma',
      lastName: 'Rodriguez',
      position: 'Creative Director'
    },
    email: '<EMAIL>',
    phone: {
      primary: '******-0401'
    },
    address: {
      street: '321 Garden Lane',
      city: 'Portland',
      state: 'OR',
      zipCode: '97201',
      country: 'United States'
    },
    businessInfo: {
      category: 'Decoration & Flowers',
      description: 'Custom floral arrangements and event decoration services for weddings and special events.',
      website: 'https://floralelegance.com',
      yearsInBusiness: 6,
      employeeCount: '1-5'
    },
    services: [
      {
        name: 'Wedding Florals',
        description: 'Bridal bouquets, centerpieces, and ceremony decorations',
        price: { min: 500, max: 3000, currency: 'USD' },
        duration: 'Per event',
        availability: 'Available'
      }
    ],
    status: 'pending'
  },
  {
    businessName: 'Dream Wedding Photography',
    contactPerson: {
      firstName: 'David',
      lastName: 'Thompson',
      position: 'Lead Photographer'
    },
    email: '<EMAIL>',
    phone: {
      primary: '******-0501'
    },
    address: {
      street: '654 Photo Studio Dr',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      country: 'United States'
    },
    businessInfo: {
      category: 'Photography',
      description: 'Professional wedding and event photography with artistic flair.',
      website: 'https://dreamweddingphoto.com',
      yearsInBusiness: 4,
      employeeCount: '1-5'
    },
    status: 'rejected',
    approvalInfo: {
      rejectedAt: new Date('2024-02-10'),
      rejectionReason: 'Insufficient portfolio samples',
      notes: 'Please reapply with more portfolio examples'
    }
  },
  {
    businessName: 'Luxury Event Transportation',
    contactPerson: {
      firstName: 'Robert',
      lastName: 'Wilson',
      position: 'Fleet Manager'
    },
    email: '<EMAIL>',
    phone: {
      primary: '******-0601'
    },
    address: {
      street: '987 Transport Way',
      city: 'Las Vegas',
      state: 'NV',
      zipCode: '89101',
      country: 'United States'
    },
    businessInfo: {
      category: 'Transportation',
      description: 'Premium transportation services including limousines, party buses, and luxury cars.',
      website: 'https://luxuryeventtrans.com',
      yearsInBusiness: 15,
      employeeCount: '21-50'
    },
    services: [
      {
        name: 'Limousine Service',
        description: 'Luxury limousine rental for special events',
        price: { min: 100, max: 300, currency: 'USD' },
        duration: 'Per hour',
        availability: 'Available'
      }
    ],
    status: 'approved',
    approvalInfo: {
      approvedAt: new Date('2024-01-20'),
      notes: 'Excellent safety record and fleet quality'
    },
    rating: {
      average: 4.9,
      count: 45
    }
  }
];

const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB for seeding');

    // Clear existing data
    await User.deleteMany({});
    await Vendor.deleteMany({});
    console.log('🗑️ Cleared existing data');

    // Insert seed data
    const users = await User.insertMany(seedUsers);
    console.log(`👥 Created ${users.length} users`);

    // Set admin user as approver for approved vendors
    const adminUser = users.find(user => user.role === 'admin');
    
    // Update approved vendors with admin user ID
    seedVendors.forEach(vendor => {
      if (vendor.status === 'approved' && vendor.approvalInfo) {
        vendor.approvalInfo.approvedBy = adminUser._id;
      }
      if (vendor.status === 'rejected' && vendor.approvalInfo) {
        vendor.approvalInfo.rejectedBy = adminUser._id;
      }
    });

    const vendors = await Vendor.insertMany(seedVendors);
    console.log(`🏢 Created ${vendors.length} vendors`);

    console.log('🎉 Database seeded successfully!');
    console.log('\n📊 Summary:');
    console.log(`   Users: ${users.length} (1 admin, 2 users, 1 moderator, 1 inactive)`);
    console.log(`   Vendors: ${vendors.length} (2 approved, 2 pending, 1 rejected)`);
    
    process.exit(0);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
};

// Run seeder if called directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase, seedUsers, seedVendors };
