const mongoose = require('mongoose');

const securityLogSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: false // Some security events might not have a user (e.g., failed login attempts)
    },
    action: {
        type: String,
        required: true,
        enum: [
            'LOGIN_SUCCESS',
            'LOGIN_FAILED',
            'LOGOUT',
            'PASSWORD_CHANGED',
            'PASSWORD_RESET_REQUESTED',
            'PASSWORD_RESET_COMPLETED',
            'TWO_FACTOR_ENABLED',
            'TWO_FACTOR_DISABLED',
            'ACCOUNT_LOCKED',
            'ACCOUNT_UNLOCKED',
            'ROLE_CHANGED',
            'PROFILE_UPDATED',
            'INVALID_PASSWORD_CHANGE_ATTEMPT',
            'INVALID_2FA_DISABLE_ATTEMPT',
            'SUSPICIOUS_ACTIVITY',
            'DATA_EXPORT',
            'BULK_ACTION',
            'ADMIN_ACCESS',
            'UNAUTHORIZED_ACCESS_ATTEMPT'
        ]
    },
    details: {
        type: String,
        required: true
    },
    ipAddress: {
        type: String,
        required: true
    },
    userAgent: {
        type: String,
        required: false
    },
    location: {
        country: String,
        city: String,
        coordinates: {
            lat: Number,
            lng: Number
        }
    },
    severity: {
        type: String,
        enum: ['low', 'medium', 'high', 'critical'],
        default: 'low'
    },
    metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    timestamp: {
        type: Date,
        default: Date.now,
        index: true
    }
}, {
    timestamps: true
});

// Indexes for better query performance
securityLogSchema.index({ userId: 1, timestamp: -1 });
securityLogSchema.index({ action: 1, timestamp: -1 });
securityLogSchema.index({ ipAddress: 1, timestamp: -1 });
securityLogSchema.index({ severity: 1, timestamp: -1 });

// TTL index to automatically delete old logs after 1 year
securityLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 365 * 24 * 60 * 60 });

// Static method to log security events
securityLogSchema.statics.logEvent = async function(data) {
    try {
        const log = new this(data);
        await log.save();
        
        // If it's a high severity event, you could trigger alerts here
        if (data.severity === 'high' || data.severity === 'critical') {
            console.warn(`🚨 High severity security event: ${data.action} - ${data.details}`);
            // Here you could send notifications, emails, etc.
        }
        
        return log;
    } catch (error) {
        console.error('Failed to log security event:', error);
        throw error;
    }
};

// Instance method to get formatted timestamp
securityLogSchema.methods.getFormattedTimestamp = function() {
    return this.timestamp.toISOString();
};

// Virtual for human-readable action
securityLogSchema.virtual('actionDisplay').get(function() {
    return this.action.replace(/_/g, ' ').toLowerCase()
        .replace(/\b\w/g, l => l.toUpperCase());
});

// Virtual for risk level based on action
securityLogSchema.virtual('riskLevel').get(function() {
    const highRiskActions = [
        'ACCOUNT_LOCKED',
        'UNAUTHORIZED_ACCESS_ATTEMPT',
        'SUSPICIOUS_ACTIVITY',
        'INVALID_PASSWORD_CHANGE_ATTEMPT'
    ];
    
    const mediumRiskActions = [
        'LOGIN_FAILED',
        'PASSWORD_RESET_REQUESTED',
        'ROLE_CHANGED',
        'TWO_FACTOR_DISABLED'
    ];
    
    if (highRiskActions.includes(this.action)) return 'high';
    if (mediumRiskActions.includes(this.action)) return 'medium';
    return 'low';
});

// Pre-save middleware to set severity based on action
securityLogSchema.pre('save', function(next) {
    if (!this.severity || this.severity === 'low') {
        const criticalActions = ['UNAUTHORIZED_ACCESS_ATTEMPT', 'ACCOUNT_LOCKED'];
        const highActions = ['SUSPICIOUS_ACTIVITY', 'INVALID_PASSWORD_CHANGE_ATTEMPT'];
        const mediumActions = ['LOGIN_FAILED', 'PASSWORD_RESET_REQUESTED'];
        
        if (criticalActions.includes(this.action)) {
            this.severity = 'critical';
        } else if (highActions.includes(this.action)) {
            this.severity = 'high';
        } else if (mediumActions.includes(this.action)) {
            this.severity = 'medium';
        }
    }
    next();
});

module.exports = mongoose.model('SecurityLog', securityLogSchema);
