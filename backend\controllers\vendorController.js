const Vendor = require('../models/Vendor');
const User = require('../models/User');

// Get all vendors with pagination and filtering
const getAllVendors = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      category,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { businessName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { 'contactPerson.firstName': { $regex: search, $options: 'i' } },
        { 'contactPerson.lastName': { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) query.status = status;
    if (category) query['businessInfo.category'] = category;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [vendors, total] = await Promise.all([
      Vendor.find(query)
        .populate('approvalInfo.approvedBy', 'firstName lastName')
        .populate('approvalInfo.rejectedBy', 'firstName lastName')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      Vendor.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        vendors,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendors',
      error: error.message
    });
  }
};

// Get vendor by ID
const getVendorById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const vendor = await Vendor.findById(id)
      .populate('approvalInfo.approvedBy', 'firstName lastName')
      .populate('approvalInfo.rejectedBy', 'firstName lastName');
    
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      data: vendor
    });

  } catch (error) {
    console.error('Get vendor by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor',
      error: error.message
    });
  }
};

// Create new vendor
const createVendor = async (req, res) => {
  try {
    const vendorData = req.body;

    // Check if vendor already exists
    const existingVendor = await Vendor.findOne({ email: vendorData.email });
    if (existingVendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor with this email already exists'
      });
    }

    const vendor = new Vendor(vendorData);
    await vendor.save();

    res.status(201).json({
      success: true,
      message: 'Vendor application submitted successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Create vendor error:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create vendor',
      error: error.message
    });
  }
};

// Update vendor
const updateVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = { ...req.body };
    
    // Remove fields that shouldn't be updated directly
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;
    delete updateData.approvalInfo; // Approval info should be updated through specific endpoints

    const vendor = await Vendor.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    res.json({
      success: true,
      message: 'Vendor updated successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Update vendor error:', error);
    
    if (error.name === 'ValidationError') {
      const errors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update vendor',
      error: error.message
    });
  }
};

// Approve vendor
const approveVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;
    const adminId = req.user?.id; // Assuming auth middleware sets req.user

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (vendor.status !== 'pending' && vendor.status !== 'under_review') {
      return res.status(400).json({
        success: false,
        message: `Cannot approve vendor with status: ${vendor.status}`
      });
    }

    vendor.status = 'approved';
    vendor.approvalInfo = {
      approvedBy: adminId,
      approvedAt: new Date(),
      notes: notes || ''
    };

    await vendor.save();

    // Populate the approvedBy field for response
    await vendor.populate('approvalInfo.approvedBy', 'firstName lastName');

    res.json({
      success: true,
      message: 'Vendor approved successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Approve vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve vendor',
      error: error.message
    });
  }
};

// Reject vendor
const rejectVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason, notes } = req.body;
    const adminId = req.user?.id;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Rejection reason is required'
      });
    }

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (vendor.status !== 'pending' && vendor.status !== 'under_review') {
      return res.status(400).json({
        success: false,
        message: `Cannot reject vendor with status: ${vendor.status}`
      });
    }

    vendor.status = 'rejected';
    vendor.approvalInfo = {
      rejectedBy: adminId,
      rejectedAt: new Date(),
      rejectionReason: reason,
      notes: notes || ''
    };

    await vendor.save();

    // Populate the rejectedBy field for response
    await vendor.populate('approvalInfo.rejectedBy', 'firstName lastName');

    res.json({
      success: true,
      message: 'Vendor rejected successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Reject vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reject vendor',
      error: error.message
    });
  }
};

// Suspend vendor
const suspendVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason, notes } = req.body;
    const adminId = req.user?.id;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: 'Suspension reason is required'
      });
    }

    const vendor = await Vendor.findById(id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    if (vendor.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: 'Only approved vendors can be suspended'
      });
    }

    vendor.status = 'suspended';
    vendor.approvalInfo.rejectedBy = adminId;
    vendor.approvalInfo.rejectedAt = new Date();
    vendor.approvalInfo.rejectionReason = reason;
    vendor.approvalInfo.notes = notes || '';

    await vendor.save();

    res.json({
      success: true,
      message: 'Vendor suspended successfully',
      data: vendor
    });

  } catch (error) {
    console.error('Suspend vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to suspend vendor',
      error: error.message
    });
  }
};

// Get vendor statistics
const getVendorStats = async (req, res) => {
  try {
    const [
      totalVendors,
      pendingVendors,
      approvedVendors,
      rejectedVendors,
      suspendedVendors,
      recentApplications
    ] = await Promise.all([
      Vendor.countDocuments(),
      Vendor.countDocuments({ status: 'pending' }),
      Vendor.countDocuments({ status: 'approved' }),
      Vendor.countDocuments({ status: 'rejected' }),
      Vendor.countDocuments({ status: 'suspended' }),
      Vendor.countDocuments({ 
        createdAt: { 
          $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) 
        } 
      })
    ]);

    const vendorsByStatus = await Vendor.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const vendorsByCategory = await Vendor.aggregate([
      {
        $group: {
          _id: '$businessInfo.category',
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalVendors,
          pendingVendors,
          approvedVendors,
          rejectedVendors,
          suspendedVendors,
          recentApplications
        },
        byStatus: vendorsByStatus,
        byCategory: vendorsByCategory
      }
    });

  } catch (error) {
    console.error('Get vendor stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor statistics',
      error: error.message
    });
  }
};

module.exports = {
  getAllVendors,
  getVendorById,
  createVendor,
  updateVendor,
  approveVendor,
  rejectVendor,
  suspendVendor,
  getVendorStats
};
