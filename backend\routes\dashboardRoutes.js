const express = require('express');
const router = express.Router();
const {
  getDashboardStats,
  getSystemHealth,
  getActivityLogs
} = require('../controllers/dashboardController');

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private (Admin)
router.get('/stats', getDashboardStats);

// @route   GET /api/dashboard/health
// @desc    Get system health metrics
// @access  Private (Admin)
router.get('/health', getSystemHealth);

// @route   GET /api/dashboard/activity
// @desc    Get activity logs
// @access  Private (Admin)
router.get('/activity', getActivityLogs);

module.exports = router;
