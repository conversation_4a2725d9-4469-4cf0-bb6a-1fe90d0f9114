const express = require('express');
const { authenticateToken, requireRole } = require('../middleware/auth');
const Setting = require('../models/Setting');
const SecurityLog = require('../models/SecurityLog');
const router = express.Router();

// @route   GET /api/settings
// @desc    Get all settings
// @access  Private (Admin only)
router.get('/', authenticateToken, requireRole(['super_admin', 'admin']), async (req, res) => {
    try {
        const settings = await Setting.find().sort({ category: 1, key: 1 });
        
        // Group settings by category
        const groupedSettings = settings.reduce((acc, setting) => {
            if (!acc[setting.category]) {
                acc[setting.category] = [];
            }
            acc[setting.category].push(setting);
            return acc;
        }, {});

        res.json({
            success: true,
            data: groupedSettings
        });
    } catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve settings'
        });
    }
});

// @route   GET /api/settings/:category
// @desc    Get settings by category
// @access  Private
router.get('/:category', authenticateToken, async (req, res) => {
    try {
        const { category } = req.params;
        const settings = await Setting.find({ category }).sort({ key: 1 });

        res.json({
            success: true,
            data: settings
        });
    } catch (error) {
        console.error('Get settings by category error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve settings'
        });
    }
});

// @route   PUT /api/settings/:id
// @desc    Update a setting
// @access  Private (Admin only)
router.put('/:id', authenticateToken, requireRole(['super_admin', 'admin']), async (req, res) => {
    try {
        const { id } = req.params;
        const { value } = req.body;

        const setting = await Setting.findById(id);
        if (!setting) {
            return res.status(404).json({
                success: false,
                message: 'Setting not found'
            });
        }

        const oldValue = setting.value;
        setting.value = value;
        setting.updatedBy = req.user.id;
        setting.updatedAt = new Date();

        await setting.save();

        // Log the setting change
        await SecurityLog.logEvent({
            userId: req.user.id,
            action: 'SETTING_UPDATED',
            details: `Updated setting ${setting.key} from "${oldValue}" to "${value}"`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            severity: 'medium',
            metadata: {
                settingId: setting._id,
                settingKey: setting.key,
                oldValue,
                newValue: value
            }
        });

        res.json({
            success: true,
            message: 'Setting updated successfully',
            data: setting
        });
    } catch (error) {
        console.error('Update setting error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update setting'
        });
    }
});

// @route   POST /api/settings/bulk-update
// @desc    Update multiple settings
// @access  Private (Super Admin only)
router.post('/bulk-update', authenticateToken, requireRole(['super_admin']), async (req, res) => {
    try {
        const { settings } = req.body;

        if (!Array.isArray(settings) || settings.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Settings array is required'
            });
        }

        const updatePromises = settings.map(async (settingUpdate) => {
            const { id, value } = settingUpdate;
            const setting = await Setting.findById(id);
            
            if (setting) {
                const oldValue = setting.value;
                setting.value = value;
                setting.updatedBy = req.user.id;
                setting.updatedAt = new Date();
                await setting.save();
                
                return {
                    id,
                    key: setting.key,
                    oldValue,
                    newValue: value,
                    success: true
                };
            }
            
            return {
                id,
                success: false,
                error: 'Setting not found'
            };
        });

        const results = await Promise.all(updatePromises);

        // Log bulk update
        await SecurityLog.logEvent({
            userId: req.user.id,
            action: 'BULK_SETTINGS_UPDATE',
            details: `Bulk updated ${results.filter(r => r.success).length} settings`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            severity: 'high',
            metadata: {
                totalSettings: settings.length,
                successfulUpdates: results.filter(r => r.success).length,
                results
            }
        });

        res.json({
            success: true,
            message: 'Bulk update completed',
            data: results
        });
    } catch (error) {
        console.error('Bulk update settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update settings'
        });
    }
});

// @route   POST /api/settings/reset-category
// @desc    Reset all settings in a category to defaults
// @access  Private (Super Admin only)
router.post('/reset-category', authenticateToken, requireRole(['super_admin']), async (req, res) => {
    try {
        const { category } = req.body;

        if (!category) {
            return res.status(400).json({
                success: false,
                message: 'Category is required'
            });
        }

        const settings = await Setting.find({ category });
        
        const resetPromises = settings.map(async (setting) => {
            const oldValue = setting.value;
            setting.value = setting.defaultValue;
            setting.updatedBy = req.user.id;
            setting.updatedAt = new Date();
            await setting.save();
            
            return {
                key: setting.key,
                oldValue,
                newValue: setting.defaultValue
            };
        });

        const results = await Promise.all(resetPromises);

        // Log category reset
        await SecurityLog.logEvent({
            userId: req.user.id,
            action: 'SETTINGS_CATEGORY_RESET',
            details: `Reset all settings in category "${category}" to defaults`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            severity: 'high',
            metadata: {
                category,
                resetCount: results.length,
                results
            }
        });

        res.json({
            success: true,
            message: `All settings in category "${category}" have been reset to defaults`,
            data: results
        });
    } catch (error) {
        console.error('Reset category settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to reset category settings'
        });
    }
});

// @route   GET /api/settings/export
// @desc    Export all settings
// @access  Private (Admin only)
router.get('/export', authenticateToken, requireRole(['super_admin', 'admin']), async (req, res) => {
    try {
        const settings = await Setting.find().select('-__v -createdAt -updatedAt');

        // Log export action
        await SecurityLog.logEvent({
            userId: req.user.id,
            action: 'SETTINGS_EXPORT',
            details: 'Exported all system settings',
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            severity: 'medium'
        });

        res.json({
            success: true,
            data: settings,
            exportedAt: new Date().toISOString(),
            exportedBy: req.user.id
        });
    } catch (error) {
        console.error('Export settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to export settings'
        });
    }
});

// @route   POST /api/settings/import
// @desc    Import settings
// @access  Private (Super Admin only)
router.post('/import', authenticateToken, requireRole(['super_admin']), async (req, res) => {
    try {
        const { settings, overwrite = false } = req.body;

        if (!Array.isArray(settings) || settings.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Settings array is required'
            });
        }

        const results = {
            imported: 0,
            updated: 0,
            skipped: 0,
            errors: []
        };

        for (const settingData of settings) {
            try {
                const existingSetting = await Setting.findOne({ key: settingData.key });
                
                if (existingSetting) {
                    if (overwrite) {
                        existingSetting.value = settingData.value;
                        existingSetting.updatedBy = req.user.id;
                        existingSetting.updatedAt = new Date();
                        await existingSetting.save();
                        results.updated++;
                    } else {
                        results.skipped++;
                    }
                } else {
                    await Setting.create({
                        ...settingData,
                        createdBy: req.user.id,
                        updatedBy: req.user.id
                    });
                    results.imported++;
                }
            } catch (error) {
                results.errors.push({
                    key: settingData.key,
                    error: error.message
                });
            }
        }

        // Log import action
        await SecurityLog.logEvent({
            userId: req.user.id,
            action: 'SETTINGS_IMPORT',
            details: `Imported settings: ${results.imported} new, ${results.updated} updated, ${results.skipped} skipped`,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            severity: 'high',
            metadata: results
        });

        res.json({
            success: true,
            message: 'Settings import completed',
            data: results
        });
    } catch (error) {
        console.error('Import settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to import settings'
        });
    }
});

module.exports = router;
