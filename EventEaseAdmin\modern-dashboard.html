<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventEase Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Modern Color Palette */
            --primary-blue: #4F46E5;
            --primary-blue-light: #6366F1;
            --primary-blue-dark: #3730A3;

            --secondary-gray: #6B7280;
            --light-gray: #F3F4F6;
            --lighter-gray: #F9FAFB;
            --dark-gray: #374151;
            --darker-gray: #1F2937;

            --accent-cyan: #06B6D4;
            --accent-purple: #8B5CF6;
            --accent-pink: #EC4899;
            --accent-orange: #F59E0B;

            --success-green: #10B981;
            --warning-yellow: #F59E0B;
            --error-red: #EF4444;

            --white: #FFFFFF;
            --black: #000000;

            /* Background */
            --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --bg-card: #FFFFFF;
            --bg-sidebar: #4F46E5;

            /* Text Colors */
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --text-light: #9CA3AF;
            --text-white: #FFFFFF;

            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            min-height: 100vh;
            padding: var(--space-xl);
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            background: var(--white);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            display: flex;
            min-height: calc(100vh - 4rem);
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: var(--bg-sidebar);
            padding: var(--space-xl);
            display: flex;
            flex-direction: column;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            margin-bottom: var(--space-2xl);
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--accent-cyan);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.25rem;
        }

        .logo-text {
            color: var(--white);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .admin-tools {
            color: var(--text-white);
            opacity: 0.7;
            font-size: 0.875rem;
            margin-bottom: var(--space-2xl);
        }

        .nav-menu {
            list-style: none;
            flex: 1;
        }

        .nav-item {
            margin-bottom: var(--space-sm);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-md) var(--space-lg);
            color: var(--text-white);
            text-decoration: none;
            border-radius: var(--radius-lg);
            transition: all 0.2s ease;
            opacity: 0.8;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255, 255, 255, 0.1);
            opacity: 1;
        }

        .nav-link.active {
            background: var(--white);
            color: var(--primary-blue);
        }

        .contact-support {
            margin-top: auto;
            padding: var(--space-lg);
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .contact-support p {
            color: var(--white);
            font-size: 0.875rem;
            margin-bottom: var(--space-md);
            opacity: 0.9;
        }

        .contact-btn {
            background: var(--accent-cyan);
            color: var(--white);
            border: none;
            padding: var(--space-sm) var(--space-lg);
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .contact-btn:hover {
            background: #0891b2;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: var(--space-xl);
            background: var(--lighter-gray);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-2xl);
        }

        .welcome-section h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .welcome-section .subtitle {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: var(--space-lg);
        }

        .upload-btn {
            background: var(--primary-blue);
            color: var(--white);
            border: none;
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            transition: all 0.2s ease;
        }

        .upload-btn:hover {
            background: var(--primary-blue-dark);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            padding: var(--space-md) var(--space-md) var(--space-md) 2.5rem;
            border: 1px solid #E5E7EB;
            border-radius: var(--radius-lg);
            background: var(--white);
            width: 300px;
            font-size: 0.875rem;
        }

        .search-icon {
            position: absolute;
            left: var(--space-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }

        .notification-icon {
            position: relative;
            color: var(--text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--error-red);
            color: var(--white);
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-xl);
            margin-bottom: var(--space-xl);
        }

        /* Promo Card */
        .promo-card {
            background: linear-gradient(135deg, #22D3EE 0%, #06B6D4 100%);
            border-radius: var(--radius-2xl);
            padding: var(--space-xl);
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .promo-content h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: var(--space-sm);
        }

        .promo-content p {
            opacity: 0.9;
            margin-bottom: var(--space-lg);
            line-height: 1.5;
        }

        .promo-btn {
            background: var(--white);
            color: var(--accent-cyan);
            border: none;
            padding: var(--space-md) var(--space-lg);
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .promo-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .promo-illustration {
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 200px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            opacity: 0.8;
        }

        /* Top Products */
        .top-products {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-xl);
            box-shadow: var(--shadow-sm);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            margin-bottom: var(--space-lg);
        }

        .section-icon {
            width: 8px;
            height: 8px;
            background: var(--primary-blue);
            border-radius: 2px;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .product-list {
            list-style: none;
        }

        .product-item {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            padding: var(--space-md) 0;
            border-bottom: 1px solid #F3F4F6;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .product-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.125rem;
        }

        .product-info {
            flex: 1;
        }

        .product-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .product-category {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .product-arrow {
            color: var(--text-light);
        }

        /* Stats Section */
        .stats-section {
            margin-top: var(--space-xl);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-xl);
            margin-bottom: var(--space-xl);
        }

        .stat-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-xl);
            box-shadow: var(--shadow-sm);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
        }

        .stat-icon-wrapper {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .stat-icon-bg {
            width: 8px;
            height: 8px;
            background: var(--primary-blue);
            border-radius: 2px;
        }

        .stat-header h3 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .stat-menu {
            color: var(--text-light);
            cursor: pointer;
        }

        .stat-content {
            margin-bottom: var(--space-lg);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: var(--space-sm);
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--success-green);
        }

        .stat-change.negative {
            color: var(--error-red);
        }

        .stat-chart {
            height: 30px;
        }

        .mini-chart {
            width: 100%;
            height: 100%;
        }

        /* Charts Grid */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--space-xl);
        }

        .chart-card, .balance-card {
            background: var(--white);
            border-radius: var(--radius-2xl);
            padding: var(--space-xl);
            box-shadow: var(--shadow-sm);
        }

        .chart-header, .balance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
        }

        .chart-title-wrapper, .balance-title-wrapper {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .chart-icon-bg, .balance-icon-bg {
            width: 8px;
            height: 8px;
            background: var(--primary-blue);
            border-radius: 2px;
        }

        .chart-header h3, .balance-header h3 {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .chart-content {
            height: 200px;
            position: relative;
        }

        .chart-placeholder {
            height: 100%;
            position: relative;
        }

        .line-chart {
            width: 100%;
            height: 100%;
        }

        .chart-label {
            font-size: 12px;
            fill: var(--text-primary);
            font-weight: 600;
        }

        .chart-days {
            display: flex;
            justify-content: space-between;
            margin-top: var(--space-md);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        /* Balance Card */
        .balance-content {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--space-xl);
        }

        .balance-item {
            text-align: center;
        }

        .balance-amount {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .balance-label {
            font-size: 0.875rem;
            font-weight: 500;
            padding: var(--space-xs) var(--space-md);
            border-radius: var(--radius-lg);
        }

        .balance-label.available {
            background: #ECFDF5;
            color: var(--success-green);
        }

        .balance-label.pending {
            background: #FEF3C7;
            color: var(--warning-yellow);
        }

        .withdraw-btn {
            width: 100%;
            background: var(--primary-blue);
            color: var(--white);
            border: none;
            padding: var(--space-md);
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .withdraw-btn:hover {
            background: var(--primary-blue-dark);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: var(--space-lg);
            }

            .nav-menu {
                display: flex;
                overflow-x: auto;
                gap: var(--space-sm);
            }

            .nav-item {
                margin-bottom: 0;
                white-space: nowrap;
            }

            .contact-support {
                display: none;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: var(--space-md);
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .header {
                flex-direction: column;
                gap: var(--space-lg);
                align-items: stretch;
            }

            .header-actions {
                flex-direction: column;
                gap: var(--space-md);
            }

            .search-input {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-hexagon"></i>
                </div>
                <div class="logo-text">EventEase.</div>
            </div>

            <div class="admin-tools">Admin Tools</div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-section="overview">
                        <i class="fas fa-chart-pie"></i>
                        Overview
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="events">
                        <i class="fas fa-calendar-alt"></i>
                        Events
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="bookings">
                        <i class="fas fa-ticket-alt"></i>
                        Bookings
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="vendors">
                        <i class="fas fa-store"></i>
                        Vendors
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="users">
                        <i class="fas fa-users"></i>
                        Users
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-line"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
            </ul>

            <div class="contact-support">
                <p>Have any problems with manage your dashboard? Try to contact our customer support</p>
                <button class="contact-btn">Contact Us</button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="header">
                <div class="welcome-section">
                    <h1>Welcome Back, <span id="adminName">Admin</span></h1>
                    <p class="subtitle">Here's what's happening with your events today</p>
                </div>

                <div class="header-actions">
                    <button class="upload-btn">
                        <i class="fas fa-upload"></i>
                        Create Event
                    </button>

                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="search-input" placeholder="Search">
                    </div>

                    <div class="notification-icon">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">2</span>
                    </div>
                </div>
            </div>

            <div class="content-grid">
                <div class="promo-card">
                    <div class="promo-content">
                        <h2>Want some EXTRA Revenue?</h2>
                        <p>Promote premium event packages and earn 15% commission on every referral</p>
                        <button class="promo-btn">Premium Program</button>
                    </div>
                    <div class="promo-illustration">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>

                <div class="top-products">
                    <div class="section-header">
                        <div class="section-icon"></div>
                        <h3 class="section-title">Top Events</h3>
                    </div>

                    <ul class="product-list">
                        <li class="product-item">
                            <div class="product-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                <i class="fas fa-music"></i>
                            </div>
                            <div class="product-info">
                                <div class="product-name">Music Festival 2024</div>
                                <div class="product-category">Music Events</div>
                            </div>
                            <i class="fas fa-chevron-right product-arrow"></i>
                        </li>
                        <li class="product-item">
                            <div class="product-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="product-info">
                                <div class="product-name">Wedding Expo</div>
                                <div class="product-category">Wedding Events</div>
                            </div>
                            <i class="fas fa-chevron-right product-arrow"></i>
                        </li>
                        <li class="product-item">
                            <div class="product-icon" style="background: linear-gradient(135deg, #4facfe, #00f2fe);">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div class="product-info">
                                <div class="product-name">Tech Conference</div>
                                <div class="product-category">Corporate Events</div>
                            </div>
                            <i class="fas fa-chevron-right product-arrow"></i>
                        </li>
                        <li class="product-item">
                            <div class="product-icon" style="background: linear-gradient(135deg, #43e97b, #38f9d7);">
                                <i class="fas fa-utensils"></i>
                            </div>
                            <div class="product-info">
                                <div class="product-name">Food Festival</div>
                                <div class="product-category">Food Events</div>
                            </div>
                            <i class="fas fa-chevron-right product-arrow"></i>
                        </li>
                        <li class="product-item">
                            <div class="product-icon" style="background: linear-gradient(135deg, #fa709a, #fee140);">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div class="product-info">
                                <div class="product-name">Art Exhibition</div>
                                <div class="product-category">Art Events</div>
                            </div>
                            <i class="fas fa-chevron-right product-arrow"></i>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Stats and Charts Section -->
            <div class="stats-section">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon-bg"></div>
                                <h3>Total Events</h3>
                            </div>
                            <div class="stat-menu">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">1,134</div>
                            <div class="stat-label">Items</div>
                            <div class="stat-change positive">
                                <span>+10% this week</span>
                            </div>
                        </div>
                        <div class="stat-chart">
                            <svg viewBox="0 0 100 30" class="mini-chart">
                                <polyline points="0,25 20,20 40,15 60,10 80,5 100,8" stroke="#10B981" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div class="stat-icon-wrapper">
                                <div class="stat-icon-bg"></div>
                                <h3>Total Revenue</h3>
                            </div>
                            <div class="stat-menu">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">$4,231</div>
                            <div class="stat-label">This month</div>
                            <div class="stat-change negative">
                                <span>-22% this week</span>
                            </div>
                        </div>
                        <div class="stat-chart">
                            <svg viewBox="0 0 100 30" class="mini-chart">
                                <polyline points="0,5 20,8 40,12 60,18 80,25 100,22" stroke="#EF4444" stroke-width="2" fill="none"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title-wrapper">
                                <div class="chart-icon-bg"></div>
                                <h3>Latest Bookings</h3>
                            </div>
                        </div>
                        <div class="chart-content">
                            <div class="chart-placeholder">
                                <svg viewBox="0 0 400 200" class="line-chart">
                                    <defs>
                                        <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                            <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:0.3"/>
                                            <stop offset="100%" style="stop-color:#4F46E5;stop-opacity:0"/>
                                        </linearGradient>
                                    </defs>
                                    <polyline points="0,150 50,120 100,140 150,100 200,80 250,90 300,70 350,85 400,75"
                                              stroke="#4F46E5" stroke-width="3" fill="none"/>
                                    <polygon points="0,150 50,120 100,140 150,100 200,80 250,90 300,70 350,85 400,75 400,200 0,200"
                                             fill="url(#chartGradient)"/>
                                    <circle cx="250" cy="90" r="4" fill="#4F46E5"/>
                                    <text x="250" y="85" text-anchor="middle" class="chart-label">350</text>
                                </svg>
                                <div class="chart-days">
                                    <span>Mon</span>
                                    <span>Sun</span>
                                    <span>Tue</span>
                                    <span>Wed</span>
                                    <span>Thu</span>
                                    <span>Fri</span>
                                    <span>Sat</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="balance-card">
                        <div class="balance-header">
                            <div class="balance-title-wrapper">
                                <div class="balance-icon-bg"></div>
                                <h3>Balances</h3>
                            </div>
                            <div class="balance-menu">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                        <div class="balance-content">
                            <div class="balance-item">
                                <div class="balance-amount">$537</div>
                                <div class="balance-label available">Available</div>
                            </div>
                            <div class="balance-item">
                                <div class="balance-amount">$234</div>
                                <div class="balance-label pending">Pending</div>
                            </div>
                        </div>
                        <button class="withdraw-btn">Withdraw Money</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Navigation functionality
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                this.classList.add('active');

                // Get section name
                const section = this.dataset.section;

                // Update content based on section
                updateContent(section);
            });
        });

        function updateContent(section) {
            const welcomeSection = document.querySelector('.welcome-section h1');
            const subtitle = document.querySelector('.welcome-section .subtitle');

            switch(section) {
                case 'overview':
                    welcomeSection.innerHTML = 'Welcome Back, <span id="adminName">Admin</span>';
                    subtitle.textContent = "Here's what's happening with your events today";
                    break;
                case 'events':
                    welcomeSection.innerHTML = 'Event Management';
                    subtitle.textContent = 'Manage all your events and their details';
                    break;
                case 'bookings':
                    welcomeSection.innerHTML = 'Booking Management';
                    subtitle.textContent = 'Track and manage all event bookings';
                    break;
                case 'vendors':
                    welcomeSection.innerHTML = 'Vendor Management';
                    subtitle.textContent = 'Review and manage vendor applications';
                    break;
                case 'users':
                    welcomeSection.innerHTML = 'User Management';
                    subtitle.textContent = 'Manage user accounts and permissions';
                    break;
                case 'analytics':
                    welcomeSection.innerHTML = 'Analytics Dashboard';
                    subtitle.textContent = 'View detailed analytics and reports';
                    break;
                case 'settings':
                    welcomeSection.innerHTML = 'System Settings';
                    subtitle.textContent = 'Configure system preferences and settings';
                    break;
            }
        }

        // Notification click handler
        document.querySelector('.notification-icon').addEventListener('click', function() {
            alert('You have 2 new notifications:\n1. New vendor application received\n2. Event booking confirmation pending');
        });

        // Contact support button
        document.querySelector('.contact-btn').addEventListener('click', function() {
            alert('Contact Support:\nEmail: <EMAIL>\nPhone: +****************\nLive Chat: Available 24/7');
        });

        // Create event button
        document.querySelector('.upload-btn').addEventListener('click', function() {
            alert('Create New Event feature coming soon!');
        });

        // Premium program button
        document.querySelector('.promo-btn').addEventListener('click', function() {
            alert('Premium Program:\n• 15% commission on referrals\n• Priority vendor placement\n• Advanced analytics\n• Dedicated support');
        });

        // Withdraw money button
        document.querySelector('.withdraw-btn').addEventListener('click', function() {
            alert('Withdraw Money:\nAvailable balance: $537\nProcessing time: 1-3 business days\nMinimum withdrawal: $50');
        });

        // Product item clicks
        document.querySelectorAll('.product-item').forEach(item => {
            item.addEventListener('click', function() {
                const eventName = this.querySelector('.product-name').textContent;
                alert(`Event Details: ${eventName}\nClick to view full event management options`);
            });
        });

        // Search functionality
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value;
                if (searchTerm) {
                    alert(`Searching for: "${searchTerm}"\nSearch functionality coming soon!`);
                }
            }
        });
    </script>
</body>
</html>
