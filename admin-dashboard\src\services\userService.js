import axios from 'axios';

const API_URL = 'http://localhost:5000/api/users';

// Get auth token from localStorage
const getToken = () => {
  return localStorage.getItem('adminToken');
};

// Configure axios with auth token
const authAxios = () => {
  const token = getToken();
  
  return axios.create({
    baseURL: API_URL,
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
};

// Get all users
export const getUsers = async () => {
  try {
    const response = await authAxios().get('/');
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Get single user
export const getUser = async (userId) => {
  try {
    const response = await authAxios().get(`/${userId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Create user
export const createUser = async (userData) => {
  try {
    const response = await authAxios().post('/', userData);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Update user
export const updateUser = async (userId, userData) => {
  try {
    const response = await authAxios().put(`/${userId}`, userData);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};

// Delete user
export const deleteUser = async (userId) => {
  try {
    const response = await authAxios().delete(`/${userId}`);
    return response.data;
  } catch (error) {
    throw error.response ? error.response.data : new Error('Server error');
  }
};
