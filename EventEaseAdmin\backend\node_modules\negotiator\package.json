{"name": "negotiator", "description": "HTTP content negotiation", "version": "0.6.4", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://blog.izs.me/)"], "license": "MIT", "keywords": ["http", "content negotiation", "accept", "accept-language", "accept-encoding", "accept-charset"], "repository": "jshttp/negotiator", "devDependencies": {"eslint": "7.32.0", "eslint-plugin-markdown": "2.2.1", "mocha": "9.1.3", "nyc": "15.1.0"}, "files": ["lib/", "HISTORY.md", "LICENSE", "index.js", "README.md"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}