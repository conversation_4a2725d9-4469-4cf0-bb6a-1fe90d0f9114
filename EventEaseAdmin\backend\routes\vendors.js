const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Vendor = require('../models/Vendor');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// @route   GET /api/vendors
// @desc    Get all vendors with pagination and filtering
// @access  Private (Admin)
router.get('/', 
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('status').optional().isIn(['pending', 'approved', 'rejected', 'suspended']).withMessage('Invalid status'),
    query('category').optional().isLength({ min: 1 }).withMessage('Category cannot be empty'),
    query('search').optional().isLength({ min: 1 }).withMessage('Search term cannot be empty')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { 
        page = 1, 
        limit = 10, 
        status, 
        category, 
        search, 
        sortBy = 'createdAt', 
        sortOrder = 'desc' 
      } = req.query;

      // Build filter object
      const filter = {};
      if (status) filter.status = status;
      if (category) filter['businessInfo.category'] = category;
      if (search) {
        filter.$or = [
          { businessName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
          { 'contactPerson.firstName': { $regex: search, $options: 'i' } },
          { 'contactPerson.lastName': { $regex: search, $options: 'i' } },
          { 'businessInfo.category': { $regex: search, $options: 'i' } }
        ];
      }

      // Build sort object
      const sort = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get vendors with pagination
      const vendors = await Vendor.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('approvalInfo.approvedBy', 'firstName lastName email')
        .populate('approvalInfo.rejectedBy', 'firstName lastName email')
        .populate('metadata.createdBy', 'firstName lastName email')
        .populate('metadata.updatedBy', 'firstName lastName email');

      // Get total count for pagination
      const total = await Vendor.countDocuments(filter);
      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        message: 'Vendors retrieved successfully',
        data: {
          vendors,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   GET /api/vendors/stats
// @desc    Get vendor statistics
// @access  Private (Admin)
router.get('/stats', requireAdmin, async (req, res, next) => {
  try {
    const stats = await Vendor.getStats();
    
    // Get category distribution
    const categoryDistribution = await Vendor.aggregate([
      {
        $group: {
          _id: '$businessInfo.category',
          count: { $sum: 1 },
          averageRating: { $avg: '$rating.average' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    // Get status distribution over time (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const statusTrends = await Vendor.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
            status: '$status'
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    // Get top rated vendors
    const topRatedVendors = await Vendor.find({ 
      status: 'approved',
      'rating.count': { $gte: 1 }
    })
    .sort({ 'rating.average': -1 })
    .limit(5)
    .select('businessName rating businessInfo.category');

    res.json({
      success: true,
      message: 'Vendor statistics retrieved successfully',
      data: {
        overview: stats,
        distribution: {
          byCategory: categoryDistribution.map(item => ({
            category: item._id,
            count: item.count,
            averageRating: Math.round((item.averageRating || 0) * 10) / 10
          }))
        },
        trends: {
          statusChanges: statusTrends.map(item => ({
            date: new Date(item._id.year, item._id.month - 1, item._id.day),
            status: item._id.status,
            count: item.count
          }))
        },
        topRated: topRatedVendors
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/vendors/:id
// @desc    Get vendor by ID
// @access  Private (Admin)
router.get('/:id', requireAdmin, async (req, res, next) => {
  try {
    const vendor = await Vendor.findById(req.params.id)
      .populate('approvalInfo.approvedBy', 'firstName lastName email')
      .populate('approvalInfo.rejectedBy', 'firstName lastName email')
      .populate('metadata.createdBy', 'firstName lastName email')
      .populate('metadata.updatedBy', 'firstName lastName email')
      .populate('reviews.user', 'firstName lastName email');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: 'Vendor retrieved successfully',
      data: {
        vendor
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   PUT /api/vendors/:id/approve
// @desc    Approve vendor application
// @access  Private (Admin)
router.put('/:id/approve',
  requireAdmin,
  [
    body('notes').optional().trim().isLength({ max: 1000 }).withMessage('Notes cannot exceed 1000 characters')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { notes } = req.body;

      const vendor = await Vendor.findById(req.params.id);
      if (!vendor) {
        return res.status(404).json({
          success: false,
          message: 'Vendor not found',
          code: 'VENDOR_NOT_FOUND'
        });
      }

      if (vendor.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: 'Only pending vendors can be approved',
          code: 'INVALID_STATUS'
        });
      }

      // Update vendor status
      vendor.status = 'approved';
      vendor.approvalInfo = {
        approvedBy: req.userId,
        approvedAt: new Date(),
        notes: notes || 'Approved via admin dashboard'
      };
      vendor.metadata.updatedBy = req.userId;

      await vendor.save();

      // Populate the approvedBy field for response
      await vendor.populate('approvalInfo.approvedBy', 'firstName lastName email');

      res.json({
        success: true,
        message: 'Vendor approved successfully',
        data: {
          vendor
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   PUT /api/vendors/:id/reject
// @desc    Reject vendor application
// @access  Private (Admin)
router.put('/:id/reject',
  requireAdmin,
  [
    body('reason').notEmpty().trim().isLength({ min: 10, max: 500 }).withMessage('Rejection reason must be between 10 and 500 characters'),
    body('notes').optional().trim().isLength({ max: 1000 }).withMessage('Notes cannot exceed 1000 characters')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { reason, notes } = req.body;

      const vendor = await Vendor.findById(req.params.id);
      if (!vendor) {
        return res.status(404).json({
          success: false,
          message: 'Vendor not found',
          code: 'VENDOR_NOT_FOUND'
        });
      }

      if (vendor.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: 'Only pending vendors can be rejected',
          code: 'INVALID_STATUS'
        });
      }

      // Update vendor status
      vendor.status = 'rejected';
      vendor.approvalInfo = {
        rejectedBy: req.userId,
        rejectedAt: new Date(),
        rejectionReason: reason,
        notes: notes || 'Rejected via admin dashboard'
      };
      vendor.metadata.updatedBy = req.userId;

      await vendor.save();

      // Populate the rejectedBy field for response
      await vendor.populate('approvalInfo.rejectedBy', 'firstName lastName email');

      res.json({
        success: true,
        message: 'Vendor rejected successfully',
        data: {
          vendor
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   PUT /api/vendors/:id/suspend
// @desc    Suspend vendor
// @access  Private (Admin)
router.put('/:id/suspend',
  requireAdmin,
  [
    body('reason').notEmpty().trim().isLength({ min: 10, max: 500 }).withMessage('Suspension reason must be between 10 and 500 characters'),
    body('notes').optional().trim().isLength({ max: 1000 }).withMessage('Notes cannot exceed 1000 characters')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { reason, notes } = req.body;

      const vendor = await Vendor.findById(req.params.id);
      if (!vendor) {
        return res.status(404).json({
          success: false,
          message: 'Vendor not found',
          code: 'VENDOR_NOT_FOUND'
        });
      }

      if (vendor.status !== 'approved') {
        return res.status(400).json({
          success: false,
          message: 'Only approved vendors can be suspended',
          code: 'INVALID_STATUS'
        });
      }

      // Update vendor status
      vendor.status = 'suspended';
      vendor.approvalInfo = {
        ...vendor.approvalInfo,
        suspendedBy: req.userId,
        suspendedAt: new Date(),
        suspensionReason: reason,
        notes: notes || 'Suspended via admin dashboard'
      };
      vendor.metadata.updatedBy = req.userId;

      await vendor.save();

      res.json({
        success: true,
        message: 'Vendor suspended successfully',
        data: {
          vendor
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   PUT /api/vendors/:id/reactivate
// @desc    Reactivate suspended vendor
// @access  Private (Admin)
router.put('/:id/reactivate',
  requireAdmin,
  [
    body('notes').optional().trim().isLength({ max: 1000 }).withMessage('Notes cannot exceed 1000 characters')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { notes } = req.body;

      const vendor = await Vendor.findById(req.params.id);
      if (!vendor) {
        return res.status(404).json({
          success: false,
          message: 'Vendor not found',
          code: 'VENDOR_NOT_FOUND'
        });
      }

      if (vendor.status !== 'suspended') {
        return res.status(400).json({
          success: false,
          message: 'Only suspended vendors can be reactivated',
          code: 'INVALID_STATUS'
        });
      }

      // Update vendor status
      vendor.status = 'approved';
      vendor.approvalInfo = {
        ...vendor.approvalInfo,
        reactivatedBy: req.userId,
        reactivatedAt: new Date(),
        notes: notes || 'Reactivated via admin dashboard'
      };
      vendor.metadata.updatedBy = req.userId;

      await vendor.save();

      res.json({
        success: true,
        message: 'Vendor reactivated successfully',
        data: {
          vendor
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   DELETE /api/vendors/:id
// @desc    Delete vendor
// @access  Private (Admin)
router.delete('/:id', requireAdmin, async (req, res, next) => {
  try {
    const vendor = await Vendor.findById(req.params.id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found',
        code: 'VENDOR_NOT_FOUND'
      });
    }

    await Vendor.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'Vendor deleted successfully'
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
