<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventEase Admin Dashboard - Complete Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 60px;
        }

        .logo {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 16px;
            letter-spacing: -2px;
        }

        .subtitle {
            font-size: 24px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .platforms {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 40px;
            margin-bottom: 60px;
        }

        .platform-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .platform-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
        }

        .platform-icon {
            font-size: 32px;
        }

        .platform-title {
            font-size: 24px;
            font-weight: 700;
        }

        .platform-description {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 24px;
            line-height: 1.6;
        }

        .demo-button {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 16px 32px;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 40px;
        }

        .features-title {
            font-size: 28px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-description {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.4;
        }

        .credentials {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .credentials-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .credential-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 20px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .qr-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-top: 40px;
        }

        .qr-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 16px;
        }

        .qr-description {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            border: 2px dashed rgba(255, 255, 255, 0.4);
        }

        .mobile-instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mobile-instructions h4 {
            font-size: 18px;
            margin-bottom: 12px;
        }

        .mobile-instructions ol {
            text-align: left;
            padding-left: 20px;
        }

        .mobile-instructions li {
            margin-bottom: 8px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .platforms {
                grid-template-columns: 1fr;
            }
            
            .logo {
                font-size: 36px;
            }
            
            .subtitle {
                font-size: 20px;
            }
            
            .container {
                padding: 20px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">EventEase Admin Dashboard</div>
            <div class="subtitle">Professional Admin Dashboard - Web & Mobile</div>
            <div class="status">
                ✅ Both Platforms Running Successfully
            </div>
        </div>

        <div class="platforms">
            <!-- Web Platform -->
            <div class="platform-card">
                <div class="platform-header">
                    <div class="platform-icon">🌐</div>
                    <div class="platform-title">Web Application</div>
                </div>
                <div class="platform-description">
                    Complete responsive web application with professional dashboard interface. 
                    Features full authentication, user management, vendor management, analytics, 
                    and mobile-responsive design.
                </div>
                <a href="index.html" class="demo-button" target="_blank">
                    🚀 Launch Web App
                </a>
            </div>

            <!-- Mobile Platform -->
            <div class="platform-card">
                <div class="platform-header">
                    <div class="platform-icon">📱</div>
                    <div class="platform-title">Mobile App (Expo)</div>
                </div>
                <div class="platform-description">
                    Native mobile application built with React Native and Expo. 
                    Same professional interface optimized for mobile devices with 
                    touch-friendly controls and native performance.
                </div>
                <a href="#mobile-setup" class="demo-button">
                    📲 Setup Mobile App
                </a>
            </div>
        </div>

        <div class="features">
            <div class="features-title">🎯 Complete Feature Set</div>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">🔐</div>
                    <div class="feature-title">Authentication</div>
                    <div class="feature-description">Secure login/register with role-based access control</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">👥</div>
                    <div class="feature-title">User Management</div>
                    <div class="feature-description">Complete CRUD operations for user administration</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🏢</div>
                    <div class="feature-title">Vendor Management</div>
                    <div class="feature-description">Vendor approval workflow and business management</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">Analytics Dashboard</div>
                    <div class="feature-description">Real-time statistics and comprehensive reporting</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">Mobile Responsive</div>
                    <div class="feature-description">Perfect experience on all devices and screen sizes</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">Professional UI</div>
                    <div class="feature-description">Modern design with gradients and smooth animations</div>
                </div>
            </div>
        </div>

        <div class="credentials">
            <div class="credentials-title">🔑 Demo Credentials</div>
            <div class="credential-item">📧 Email: <EMAIL></div>
            <div class="credential-item">🔑 Password: SuperAdmin123!</div>
            <div class="credential-item">🎫 Admin Code: ADMIN2024</div>
        </div>

        <div id="mobile-setup" class="qr-section">
            <div class="qr-title">📱 Mobile App Setup</div>
            <div class="qr-description">
                Scan the QR code below with the Expo Go app to run the mobile version
            </div>
            
            <div class="qr-placeholder">
                📱
            </div>
            
            <div class="mobile-instructions">
                <h4>📲 How to Run Mobile App:</h4>
                <ol>
                    <li>Install <strong>Expo Go</strong> app on your phone (iOS/Android)</li>
                    <li>Open terminal in the EventEaseAdmin folder</li>
                    <li>Run: <code>npx expo start</code></li>
                    <li>Scan the QR code that appears with Expo Go app</li>
                    <li>The mobile app will load on your device</li>
                </ol>
            </div>
            
            <div style="margin-top: 20px; padding: 16px; background: rgba(255, 255, 255, 0.1); border-radius: 12px;">
                <strong>💡 Alternative:</strong> Run <code>npx expo start --web</code> to open mobile version in browser
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 EventEase Admin Dashboard Demo loaded!');
            console.log('🌐 Web version: Click "Launch Web App" button');
            console.log('📱 Mobile version: Follow mobile setup instructions');
            
            // Add click tracking
            document.querySelectorAll('.demo-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.href.includes('index.html')) {
                        console.log('🌐 Launching web application...');
                    } else {
                        console.log('📱 Scrolling to mobile setup...');
                    }
                });
            });
        });
    </script>
</body>
</html>
