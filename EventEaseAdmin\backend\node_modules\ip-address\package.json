{"name": "ip-address", "description": "A library for parsing IPv4 and IPv6 IP addresses in node and the browser.", "keywords": ["ipv6", "ipv4", "browser", "validation"], "version": "9.0.5", "author": "<PERSON> <<EMAIL>> (https://beaugunderson.com/)", "license": "MIT", "main": "dist/ip-address.js", "types": "dist/ip-address.d.ts", "scripts": {"docs": "documentation build --github --output docs --format html ./ip-address.js", "build": "rm -rf dist; mkdir dist; tsc", "prepack": "npm run build", "release": "release-it", "test-ci": "nyc mocha", "test": "mocha", "watch": "mocha --watch"}, "nyc": {"extension": [".ts"], "exclude": ["**/*.d.ts", ".eslintrc.js", "coverage/", "dist/", "test/", "tmp/"], "reporter": ["html", "lcov", "text"], "all": true}, "engines": {"node": ">= 12"}, "files": ["src", "dist"], "repository": {"type": "git", "url": "git://github.com/beaugunderson/ip-address.git"}, "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "devDependencies": {"@types/chai": "^4.2.18", "@types/jsbn": "^1.2.31", "@types/mocha": "^10.0.1", "@types/sprintf-js": "^1.1.2", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "browserify": "^17.0.0", "chai": "^4.3.4", "codecov": "^3.8.2", "documentation": "^14.0.2", "eslint": "^8.50.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-filenames": "^1.3.2", "eslint-plugin-import": "^2.23.4", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-sort-imports-es6-autofix": "^0.6.0", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^3.0.3", "release-it": "^16.2.0", "source-map-support": "^0.5.19", "ts-node": "^10.0.0", "typescript": "^5.2.2"}}