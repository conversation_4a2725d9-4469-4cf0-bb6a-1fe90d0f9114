const bcrypt = require('bcryptjs');

// In-memory database
const db = {
  admins: [],
  users: [],
  vendors: []
};

// Initialize with a default admin
const initializeDb = async () => {
  // Check if admin already exists
  const adminExists = db.admins.find(admin => admin.email === '<EMAIL>');

  if (!adminExists) {
    // Generate salt
    const salt = await bcrypt.genSalt(10);
    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // Create default admin
    const admin = {
      _id: '1',
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin',
      createdAt: new Date()
    };

    // Add to database
    db.admins.push(admin);
    console.log('Default admin created');
  }
};

// Initialize with sample users
const initializeSampleUsers = async () => {
  // Only add sample users if none exist
  if (db.users.length === 0) {
    const sampleUsers = [
      {
        _id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '****** 567 890',
        role: 'user',
        status: 'active',
        lastLogin: '2023-05-15T10:30:00',
        registeredOn: '2023-01-10T08:15:00'
      },
      {
        _id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '****** 654 321',
        role: 'premium',
        status: 'active',
        lastLogin: '2023-05-14T14:45:00',
        registeredOn: '2023-02-20T11:30:00'
      },
      {
        _id: '3',
        name: 'Robert Johnson',
        email: '<EMAIL>',
        phone: '****** 123 456',
        role: 'user',
        status: 'inactive',
        lastLogin: '2023-04-30T09:20:00',
        registeredOn: '2023-01-05T16:45:00'
      },
      {
        _id: '4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        phone: '****** 789 012',
        role: 'premium',
        status: 'active',
        lastLogin: '2023-05-16T08:10:00',
        registeredOn: '2023-03-15T10:20:00'
      },
      {
        _id: '5',
        name: 'Michael Wilson',
        email: '<EMAIL>',
        phone: '****** 456 789',
        role: 'user',
        status: 'suspended',
        lastLogin: '2023-04-10T11:05:00',
        registeredOn: '2023-02-01T09:30:00'
      }
    ];

    // Add sample users to database
    db.users.push(...sampleUsers);
    console.log('Sample users created');
  }
};

// Initialize with sample vendors
const initializeSampleVendors = async () => {
  // Only add sample vendors if none exist
  if (db.vendors.length === 0) {
    const sampleVendors = [
      {
        _id: '1',
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '****** 567 890',
        businessName: 'Delicious Bites',
        category: 'restaurant',
        status: 'active',
        address: '123 Main St, New York, NY',
        description: 'Fine dining restaurant with Italian cuisine',
        joinedOn: '2023-01-10T08:15:00',
        rating: 4.8,
        totalOrders: 1245
      },
      {
        _id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '****** 654 321',
        businessName: 'Morning Brew Cafe',
        category: 'cafe',
        status: 'active',
        address: '456 Park Ave, Boston, MA',
        description: 'Cozy cafe with specialty coffee and pastries',
        joinedOn: '2023-02-15T10:30:00',
        rating: 4.5,
        totalOrders: 890
      },
      {
        _id: '3',
        name: 'Michael Brown',
        email: '<EMAIL>',
        phone: '****** 123 456',
        businessName: 'Sweet Treats Bakery',
        category: 'bakery',
        status: 'inactive',
        address: '789 Oak St, Chicago, IL',
        description: 'Artisan bakery specializing in cakes and pastries',
        joinedOn: '2023-01-05T16:45:00',
        rating: 4.2,
        totalOrders: 650
      },
      {
        _id: '4',
        name: 'Emily Davis',
        email: '<EMAIL>',
        phone: '****** 789 012',
        businessName: 'Quick Bites',
        category: 'fast_food',
        status: 'active',
        address: '321 Pine St, Los Angeles, CA',
        description: 'Fast food restaurant with healthy options',
        joinedOn: '2023-03-20T09:15:00',
        rating: 4.0,
        totalOrders: 1500
      },
      {
        _id: '5',
        name: 'David Wilson',
        email: '<EMAIL>',
        phone: '****** 456 789',
        businessName: 'Fresh Market',
        category: 'grocery',
        status: 'suspended',
        address: '654 Maple St, Seattle, WA',
        description: 'Organic grocery store with local produce',
        joinedOn: '2023-02-01T11:30:00',
        rating: 4.6,
        totalOrders: 780
      }
    ];

    // Add sample vendors to database
    db.vendors.push(...sampleVendors);
    console.log('Sample vendors created');
  }
};

// Initialize the database
initializeDb();
initializeSampleUsers();
initializeSampleVendors();

module.exports = db;
