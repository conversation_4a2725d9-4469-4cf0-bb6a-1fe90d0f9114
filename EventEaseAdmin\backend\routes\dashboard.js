const express = require('express');
const User = require('../models/User');
const Vendor = require('../models/Vendor');
const { requireAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private (Admin)
router.get('/stats', requireAdmin, async (req, res, next) => {
  try {
    // Get current date for time-based queries
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get user statistics
    const userStats = await User.getStats();
    
    // Get vendor statistics
    const vendorStats = await Vendor.getStats();

    // Get recent activity counts
    const recentUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    const recentVendors = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Get weekly growth
    const weeklyUserGrowth = await User.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });

    const weeklyVendorGrowth = await Vendor.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });

    // Calculate growth percentages (mock data for now)
    const userGrowthPercentage = userStats.totalUsers > 0 ? 
      Math.round((weeklyUserGrowth / userStats.totalUsers) * 100) : 0;
    
    const vendorGrowthPercentage = vendorStats.totalVendors > 0 ? 
      Math.round((weeklyVendorGrowth / vendorStats.totalVendors) * 100) : 0;

    // Mock data for bookings and events (you can replace with actual models later)
    const totalBookings = Math.floor(Math.random() * 500) + 100;
    const totalEvents = Math.floor(Math.random() * 200) + 50;
    const totalRevenue = Math.floor(Math.random() * 100000) + 10000;

    const dashboardStats = {
      overview: {
        totalUsers: userStats.totalUsers,
        totalVendors: vendorStats.totalVendors,
        totalBookings,
        totalEvents,
        totalRevenue,
        activeUsers: userStats.activeUsers,
        pendingVendors: vendorStats.pendingVendors
      },
      growth: {
        users: {
          weekly: weeklyUserGrowth,
          monthly: recentUsers,
          percentage: userGrowthPercentage
        },
        vendors: {
          weekly: weeklyVendorGrowth,
          monthly: recentVendors,
          percentage: vendorGrowthPercentage
        },
        bookings: {
          weekly: Math.floor(Math.random() * 50) + 10,
          monthly: Math.floor(Math.random() * 200) + 50,
          percentage: Math.floor(Math.random() * 20) + 5
        },
        revenue: {
          weekly: Math.floor(Math.random() * 10000) + 2000,
          monthly: Math.floor(Math.random() * 40000) + 8000,
          percentage: Math.floor(Math.random() * 15) + 3
        }
      },
      userBreakdown: {
        byRole: {
          admin: userStats.adminUsers,
          user: userStats.totalUsers - userStats.adminUsers
        },
        byStatus: {
          active: userStats.activeUsers,
          inactive: userStats.totalUsers - userStats.activeUsers
        }
      },
      vendorBreakdown: {
        byStatus: {
          pending: vendorStats.pendingVendors,
          approved: vendorStats.approvedVendors,
          rejected: vendorStats.rejectedVendors,
          suspended: vendorStats.suspendedVendors
        },
        averageRating: vendorStats.averageRating || 0
      },
      recentActivity: {
        newUsersToday: await User.countDocuments({
          createdAt: { $gte: new Date(now.setHours(0, 0, 0, 0)) }
        }),
        newVendorsToday: await Vendor.countDocuments({
          createdAt: { $gte: new Date(now.setHours(0, 0, 0, 0)) }
        }),
        pendingApprovals: vendorStats.pendingVendors
      }
    };

    res.json({
      success: true,
      message: 'Dashboard statistics retrieved successfully',
      data: dashboardStats
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/dashboard/analytics
// @desc    Get detailed analytics data
// @access  Private (Admin)
router.get('/analytics', requireAdmin, async (req, res, next) => {
  try {
    const { period = '30d', type = 'all' } = req.query;
    
    // Calculate date range based on period
    let startDate;
    const endDate = new Date();
    
    switch (period) {
      case '7d':
        startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(endDate.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    const analytics = {};

    // User analytics
    if (type === 'all' || type === 'users') {
      const userAnalytics = await User.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 },
            roles: {
              $push: '$role'
            }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
        }
      ]);

      analytics.users = userAnalytics.map(item => ({
        date: new Date(item._id.year, item._id.month - 1, item._id.day),
        count: item.count,
        adminCount: item.roles.filter(role => ['admin', 'super_admin'].includes(role)).length,
        userCount: item.roles.filter(role => role === 'user').length
      }));
    }

    // Vendor analytics
    if (type === 'all' || type === 'vendors') {
      const vendorAnalytics = await Vendor.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate, $lte: endDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 },
            statuses: {
              $push: '$status'
            },
            categories: {
              $push: '$businessInfo.category'
            }
          }
        },
        {
          $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
        }
      ]);

      analytics.vendors = vendorAnalytics.map(item => ({
        date: new Date(item._id.year, item._id.month - 1, item._id.day),
        count: item.count,
        pendingCount: item.statuses.filter(status => status === 'pending').length,
        approvedCount: item.statuses.filter(status => status === 'approved').length,
        rejectedCount: item.statuses.filter(status => status === 'rejected').length
      }));
    }

    // Category distribution for vendors
    if (type === 'all' || type === 'categories') {
      const categoryStats = await Vendor.aggregate([
        {
          $match: {
            status: 'approved'
          }
        },
        {
          $group: {
            _id: '$businessInfo.category',
            count: { $sum: 1 },
            averageRating: { $avg: '$rating.average' }
          }
        },
        {
          $sort: { count: -1 }
        }
      ]);

      analytics.categories = categoryStats.map(item => ({
        category: item._id,
        count: item.count,
        averageRating: Math.round((item.averageRating || 0) * 10) / 10
      }));
    }

    res.json({
      success: true,
      message: 'Analytics data retrieved successfully',
      data: {
        period,
        startDate,
        endDate,
        analytics
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/dashboard/recent-activity
// @desc    Get recent activity feed
// @access  Private (Admin)
router.get('/recent-activity', requireAdmin, async (req, res, next) => {
  try {
    const { limit = 20, page = 1 } = req.query;
    const skip = (page - 1) * limit;

    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(parseInt(limit) / 2)
      .skip(skip)
      .select('firstName lastName email role createdAt');

    // Get recent vendors
    const recentVendors = await Vendor.find()
      .sort({ createdAt: -1 })
      .limit(parseInt(limit) / 2)
      .skip(skip)
      .select('businessName email status createdAt businessInfo.category');

    // Combine and sort activities
    const activities = [
      ...recentUsers.map(user => ({
        type: 'user_registered',
        id: user._id,
        title: `New ${user.role} registered`,
        description: `${user.firstName} ${user.lastName} (${user.email})`,
        timestamp: user.createdAt,
        metadata: {
          userRole: user.role,
          email: user.email
        }
      })),
      ...recentVendors.map(vendor => ({
        type: 'vendor_application',
        id: vendor._id,
        title: 'New vendor application',
        description: `${vendor.businessName} - ${vendor.businessInfo.category}`,
        timestamp: vendor.createdAt,
        metadata: {
          status: vendor.status,
          category: vendor.businessInfo.category,
          email: vendor.email
        }
      }))
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
     .slice(0, parseInt(limit));

    res.json({
      success: true,
      message: 'Recent activity retrieved successfully',
      data: {
        activities,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: activities.length
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
