<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventEase Admin Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* Primary Colors - Modern Blue */
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-200: #bfdbfe;
            --primary-300: #93c5fd;
            --primary-400: #60a5fa;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-800: #1e40af;
            --primary-900: #1e3a8a;

            /* Secondary Colors - Sophisticated Gray */
            --secondary-50: #f8fafc;
            --secondary-100: #f1f5f9;
            --secondary-200: #e2e8f0;
            --secondary-300: #cbd5e1;
            --secondary-400: #94a3b8;
            --secondary-500: #64748b;
            --secondary-600: #475569;
            --secondary-700: #334155;
            --secondary-800: #1e293b;
            --secondary-900: #0f172a;

            /* Accent Colors - Purple */
            --accent-50: #faf5ff;
            --accent-100: #f3e8ff;
            --accent-200: #e9d5ff;
            --accent-300: #d8b4fe;
            --accent-400: #c084fc;
            --accent-500: #a855f7;
            --accent-600: #9333ea;
            --accent-700: #7c3aed;
            --accent-800: #6b21a8;
            --accent-900: #581c87;

            /* Success Colors - Emerald */
            --success-50: #ecfdf5;
            --success-100: #d1fae5;
            --success-200: #a7f3d0;
            --success-300: #6ee7b7;
            --success-400: #34d399;
            --success-500: #10b981;
            --success-600: #059669;
            --success-700: #047857;
            --success-800: #065f46;
            --success-900: #064e3b;

            /* Warning Colors - Amber */
            --warning-50: #fffbeb;
            --warning-100: #fef3c7;
            --warning-200: #fde68a;
            --warning-300: #fcd34d;
            --warning-400: #fbbf24;
            --warning-500: #f59e0b;
            --warning-600: #d97706;
            --warning-700: #b45309;
            --warning-800: #92400e;
            --warning-900: #78350f;

            /* Error Colors - Red */
            --error-50: #fef2f2;
            --error-100: #fee2e2;
            --error-200: #fecaca;
            --error-300: #fca5a5;
            --error-400: #f87171;
            --error-500: #ef4444;
            --error-600: #dc2626;
            --error-700: #b91c1c;
            --error-800: #991b1b;
            --error-900: #7f1d1d;

            /* Info Colors - Cyan */
            --info-50: #ecfeff;
            --info-100: #cffafe;
            --info-200: #a5f3fc;
            --info-300: #67e8f9;
            --info-400: #22d3ee;
            --info-500: #06b6d4;
            --info-600: #0891b2;
            --info-700: #0e7490;
            --info-800: #155e75;
            --info-900: #164e63;

            /* Shadows */
            --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* Typography */
            --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

            /* Spacing */
            --space-px: 1px;
            --space-0: 0;
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;
            --space-24: 6rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family-sans);
            background: linear-gradient(135deg, var(--secondary-50) 0%, var(--primary-50) 50%, var(--accent-50) 100%);
            color: var(--secondary-800);
            line-height: 1.6;
            font-weight: 400;
            letter-spacing: -0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* Authentication Styles */
        .auth-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--accent-600) 50%, var(--primary-800) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-6);
            position: relative;
            overflow: hidden;
        }

        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: var(--radius-2xl);
            padding: var(--space-12);
            width: 100%;
            max-width: 480px;
            box-shadow: var(--shadow-2xl);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .auth-header {
            text-align: center;
            margin-bottom: var(--space-10);
        }

        .auth-logo {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .auth-subtitle {
            font-size: 1.125rem;
            color: var(--secondary-600);
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .input-group {
            margin-bottom: var(--space-6);
        }

        .input-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--secondary-700);
            margin-bottom: var(--space-2);
            display: block;
            letter-spacing: -0.01em;
        }

        .auth-input {
            width: 100%;
            height: 3.5rem;
            border: 2px solid var(--secondary-200);
            border-radius: var(--radius-xl);
            padding: 0 var(--space-5);
            font-size: 1rem;
            background: var(--secondary-50);
            color: var(--secondary-800);
            font-weight: 500;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: var(--font-family-sans);
            letter-spacing: -0.01em;
        }

        .auth-input:focus {
            outline: none;
            border-color: var(--primary-500);
            background: white;
            box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1);
            transform: translateY(-1px);
        }

        .auth-input::placeholder {
            color: var(--secondary-400);
            font-weight: 400;
        }

        .auth-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            height: 3.5rem;
            border: none;
            border-radius: var(--radius-xl);
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: var(--space-4);
            margin-bottom: var(--space-6);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: var(--font-family-sans);
            letter-spacing: -0.01em;
            box-shadow: var(--shadow-md);
        }

        .auth-button:hover {
            background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .auth-button:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }

        .auth-button:disabled {
            background: var(--secondary-300);
            cursor: not-allowed;
            transform: none;
            box-shadow: var(--shadow-sm);
        }

        .auth-switch {
            text-align: center;
            margin-top: var(--space-4);
        }

        .auth-switch-text {
            font-size: 0.875rem;
            color: var(--secondary-600);
            font-weight: 400;
        }

        .auth-switch-link {
            color: var(--primary-600);
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .auth-switch-link:hover {
            color: var(--primary-700);
        }

        /* Dashboard Styles */
        .dashboard-container {
            display: none;
            min-height: 100vh;
            flex-direction: column;
            background: var(--secondary-50);
        }

        .dashboard-container.active {
            display: flex;
        }

        .top-bar {
            height: 5rem;
            background: white;
            border-bottom: 1px solid var(--secondary-200);
            display: flex;
            align-items: center;
            padding: 0 var(--space-8);
            box-shadow: var(--shadow-sm);
            backdrop-filter: blur(8px);
            position: sticky;
            top: 0;
            z-index: 50;
        }

        .logo-section {
            flex: 1;
        }

        .top-logo {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--secondary-900);
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .top-subtitle {
            font-size: 0.75rem;
            color: var(--secondary-500);
            margin-top: var(--space-1);
            font-weight: 500;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .profile-section {
            display: flex;
            align-items: center;
            gap: var(--space-4);
        }

        .profile-info {
            text-align: right;
        }

        .profile-name {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--secondary-800);
            letter-spacing: -0.01em;
        }

        .profile-role {
            font-size: 0.75rem;
            color: var(--secondary-500);
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.05em;
            margin-top: var(--space-1);
        }

        .logout-btn {
            background: linear-gradient(135deg, var(--error-500), var(--error-600));
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-lg);
            cursor: pointer;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-sm);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, var(--error-600), var(--error-700));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .body-container {
            flex: 1;
            display: flex;
        }

        .sidebar {
            width: 18rem;
            background: white;
            border-right: 1px solid var(--secondary-200);
            padding: var(--space-6) var(--space-4);
            overflow-y: auto;
            box-shadow: var(--shadow-sm);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: var(--space-4) var(--space-5);
            margin-bottom: var(--space-2);
            border-radius: var(--radius-xl);
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            color: var(--secondary-700);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .menu-item:hover {
            background: var(--secondary-50);
            color: var(--secondary-800);
            transform: translateX(4px);
        }

        .menu-item:hover::before {
            opacity: 1;
        }

        .menu-item.active {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            color: white;
            box-shadow: var(--shadow-lg);
            transform: translateX(4px);
        }

        .menu-item.active::before {
            opacity: 0;
        }

        .menu-icon {
            font-size: 1.125rem;
            margin-right: var(--space-4);
            width: 1.5rem;
            position: relative;
            z-index: 1;
        }

        .menu-text {
            font-size: 0.875rem;
            font-weight: 600;
            letter-spacing: -0.01em;
            position: relative;
            z-index: 1;
        }

        .main-content {
            flex: 1;
            background: var(--secondary-50);
            overflow-y: auto;
        }

        .content-header {
            background: white;
            padding: var(--space-8);
            border-bottom: 1px solid var(--secondary-200);
            box-shadow: var(--shadow-xs);
        }

        .content-title {
            font-size: 2rem;
            font-weight: 800;
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            letter-spacing: -0.02em;
        }

        .breadcrumb {
            font-size: 0.875rem;
            color: var(--secondary-500);
            font-weight: 500;
        }

        .breadcrumb span {
            color: var(--secondary-600);
        }

        .content-area {
            padding: var(--space-8);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-10);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--secondary-200);
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-2xl);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card.users { background: linear-gradient(135deg, var(--primary-500), var(--primary-700)); }
        .stat-card.vendors { background: linear-gradient(135deg, var(--accent-500), var(--accent-700)); }
        .stat-card.bookings { background: linear-gradient(135deg, var(--info-500), var(--info-700)); }
        .stat-card.revenue { background: linear-gradient(135deg, var(--success-500), var(--success-700)); }

        .stat-content {
            flex: 1;
            position: relative;
            z-index: 1;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: var(--space-1);
            letter-spacing: -0.02em;
            font-family: var(--font-family-mono);
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 600;
            letter-spacing: -0.01em;
        }

        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
            position: relative;
            z-index: 1;
        }

        /* Data Lists */
        .data-list {
            background: white;
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--secondary-200);
        }

        .data-item {
            display: flex;
            align-items: center;
            padding: var(--space-5) 0;
            border-bottom: 1px solid var(--secondary-100);
            transition: all 0.2s ease;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-item:hover {
            background: var(--secondary-50);
            margin: 0 calc(-1 * var(--space-6));
            padding-left: var(--space-6);
            padding-right: var(--space-6);
            border-radius: var(--radius-lg);
        }

        .data-avatar {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            margin-right: var(--space-4);
            font-size: 0.875rem;
            letter-spacing: -0.01em;
            box-shadow: var(--shadow-md);
        }

        .data-info {
            flex: 1;
        }

        .data-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--secondary-900);
            margin-bottom: var(--space-1);
            letter-spacing: -0.01em;
        }

        .data-email {
            font-size: 0.875rem;
            color: var(--secondary-600);
            margin-bottom: var(--space-1);
            font-family: var(--font-family-mono);
        }

        .data-role {
            font-size: 0.75rem;
            color: var(--primary-600);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge {
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-lg);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow: var(--shadow-sm);
        }

        .status-pending {
            background: var(--warning-100);
            color: var(--warning-600);
            border: 1px solid var(--warning-200);
        }
        .status-approved {
            background: var(--success-100);
            color: var(--success-700);
            border: 1px solid var(--success-200);
        }
        .status-active {
            background: var(--primary-100);
            color: var(--primary-700);
            border: 1px solid var(--primary-200);
        }
        .status-inactive {
            background: var(--secondary-100);
            color: var(--secondary-600);
            border: 1px solid var(--secondary-200);
        }
        .status-rejected {
            background: var(--error-100);
            color: var(--error-700);
            border: 1px solid var(--error-200);
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: var(--space-2);
            align-items: center;
        }

        .btn {
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
            border: none;
            font-size: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            gap: var(--space-1);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-500), var(--success-600));
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, var(--success-600), var(--success-700));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--error-500), var(--error-600));
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, var(--error-600), var(--error-700));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, var(--warning-600), var(--warning-700));
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--secondary-100);
            color: var(--secondary-700);
            border: 1px solid var(--secondary-200);
        }

        .btn-secondary:hover {
            background: var(--secondary-200);
            color: var(--secondary-800);
            transform: translateY(-1px);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-2xl);
            transform: scale(0.9);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal.active .modal-content {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
            padding-bottom: var(--space-4);
            border-bottom: 1px solid var(--secondary-200);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--secondary-900);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--secondary-400);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: var(--secondary-100);
            color: var(--secondary-600);
        }

        .modal-body {
            margin-bottom: var(--space-6);
        }

        .modal-footer {
            display: flex;
            gap: var(--space-3);
            justify-content: flex-end;
            padding-top: var(--space-4);
            border-top: 1px solid var(--secondary-200);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: var(--space-4);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--secondary-700);
            margin-bottom: var(--space-2);
        }

        .form-input {
            width: 100%;
            padding: var(--space-3);
            border: 2px solid var(--secondary-200);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            background: var(--secondary-50);
            color: var(--secondary-800);
            transition: all 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-500);
            background: white;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            width: 100%;
            padding: var(--space-3);
            border: 2px solid var(--secondary-200);
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            background: var(--secondary-50);
            color: var(--secondary-800);
            transition: all 0.2s ease;
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-500);
            background: white;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }

        .hidden {
            display: none;
        }

        .loading {
            text-align: center;
            padding: var(--space-10);
            color: var(--secondary-500);
            font-weight: 500;
            font-size: 1rem;
        }

        .notification {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            padding: var(--space-4) var(--space-6);
            border-radius: var(--radius-xl);
            color: white;
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(8px);
            font-size: 0.875rem;
            letter-spacing: -0.01em;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification.success {
            background: linear-gradient(135deg, var(--success-500), var(--success-600));
        }
        .notification.error {
            background: linear-gradient(135deg, var(--error-500), var(--error-600));
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%) scale(0.95);
                opacity: 0;
            }
            to {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0) scale(1);
                opacity: 1;
            }
            to {
                transform: translateX(100%) scale(0.95);
                opacity: 0;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: fixed;
                top: 5rem;
                left: -100%;
                height: calc(100vh - 5rem);
                z-index: 1000;
                transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                box-shadow: var(--shadow-2xl);
            }

            .sidebar.open {
                left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }

            .auth-card {
                padding: var(--space-8);
                margin: var(--space-4);
            }

            .top-bar {
                padding: 0 var(--space-4);
                height: 4rem;
            }

            .content-area {
                padding: var(--space-4);
            }

            .content-header {
                padding: var(--space-6) var(--space-4);
            }

            .content-title {
                font-size: 1.5rem;
            }

            .stat-card {
                padding: var(--space-6);
            }

            .stat-number {
                font-size: 1.75rem;
            }

            .notification {
                top: var(--space-4);
                right: var(--space-4);
                left: var(--space-4);
                transform: translateY(-100%);
                animation: slideInMobile 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            @keyframes slideInMobile {
                from {
                    transform: translateY(-100%) scale(0.95);
                    opacity: 0;
                }
                to {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                }
            }
        }

        @media (max-width: 480px) {
            .auth-logo {
                font-size: 2rem;
            }

            .auth-card {
                padding: var(--space-6);
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .data-item:hover {
                margin: 0 calc(-1 * var(--space-4));
                padding-left: var(--space-4);
                padding-right: var(--space-4);
            }
        }
    </style>
</head>
<body>
    <!-- Authentication Screen -->
    <div id="authContainer" class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">EventEase</div>
                <div class="auth-subtitle">Admin Dashboard</div>
            </div>

            <div id="loginForm">
                <div class="input-group">
                    <label class="input-label">Email Address</label>
                    <input type="email" class="auth-input" id="loginEmail" value="<EMAIL>" placeholder="Enter your email">
                </div>

                <div class="input-group">
                    <label class="input-label">Password</label>
                    <input type="password" class="auth-input" id="loginPassword" value="SuperAdmin123!" placeholder="Enter your password">
                </div>

                <button class="auth-button" id="loginBtn" onclick="handleLogin()">Sign In</button>

                <div class="auth-switch">
                    <span class="auth-switch-text">
                        Don't have an account?
                        <a href="#" class="auth-switch-link" onclick="showRegister()">Register here</a>
                    </span>
                </div>
            </div>

            <div id="registerForm" class="hidden">
                <div class="input-group">
                    <label class="input-label">First Name</label>
                    <input type="text" class="auth-input" id="regFirstName" placeholder="First name">
                </div>

                <div class="input-group">
                    <label class="input-label">Last Name</label>
                    <input type="text" class="auth-input" id="regLastName" placeholder="Last name">
                </div>

                <div class="input-group">
                    <label class="input-label">Email Address</label>
                    <input type="email" class="auth-input" id="regEmail" placeholder="Enter your email">
                </div>

                <div class="input-group">
                    <label class="input-label">Password</label>
                    <input type="password" class="auth-input" id="regPassword" placeholder="Enter your password">
                </div>

                <div class="input-group">
                    <label class="input-label">Admin Registration Code</label>
                    <input type="text" class="auth-input" id="regAdminCode" value="ADMIN2024" placeholder="Enter admin code">
                </div>

                <button class="auth-button" onclick="handleRegister()">Create Account</button>

                <div class="auth-switch">
                    <span class="auth-switch-text">
                        Already have an account?
                        <a href="#" class="auth-switch-link" onclick="showLogin()">Sign in here</a>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Screen -->
    <div id="dashboardContainer" class="dashboard-container">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="logo-section">
                <div class="top-logo">EventEase</div>
                <div class="top-subtitle">Admin Dashboard</div>
            </div>

            <div class="profile-section">
                <div class="profile-info">
                    <div class="profile-name" id="profileName">Super Admin</div>
                    <div class="profile-role" id="profileRole">Administrator</div>
                </div>
                <button class="logout-btn" onclick="handleLogout()">Logout</button>
            </div>
        </div>

        <!-- Body Container -->
        <div class="body-container">
            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="menu-item active" onclick="showSection('dashboard', this)">
                    <i class="menu-icon fas fa-chart-line"></i>
                    <span class="menu-text">Dashboard</span>
                </div>
                <div class="menu-item" onclick="showSection('users', this)">
                    <i class="menu-icon fas fa-users"></i>
                    <span class="menu-text">User Management</span>
                </div>
                <div class="menu-item" onclick="showSection('vendors', this)">
                    <i class="menu-icon fas fa-building"></i>
                    <span class="menu-text">Vendor Management</span>
                </div>
                <div class="menu-item" onclick="showSection('events', this)">
                    <i class="menu-icon fas fa-calendar-star"></i>
                    <span class="menu-text">Events</span>
                </div>
                <div class="menu-item" onclick="showSection('bookings', this)">
                    <i class="menu-icon fas fa-calendar-check"></i>
                    <span class="menu-text">Bookings</span>
                </div>
                <div class="menu-item" onclick="showSection('analytics', this)">
                    <i class="menu-icon fas fa-chart-bar"></i>
                    <span class="menu-text">Analytics</span>
                </div>
                <div class="menu-item" onclick="showSection('settings', this)">
                    <i class="menu-icon fas fa-cog"></i>
                    <span class="menu-text">Settings</span>
                </div>
                <div class="menu-item" onclick="showSection('reports', this)">
                    <i class="menu-icon fas fa-file-alt"></i>
                    <span class="menu-text">Reports</span>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-header">
                    <div class="content-title" id="contentTitle">Dashboard</div>
                    <div class="breadcrumb">
                        <span>Dashboard</span> › <span id="breadcrumbActive">Overview</span>
                    </div>
                </div>

                <div class="content-area" id="contentArea">
                    <div class="loading">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="confirmModalTitle">Confirm Action</h3>
                <button class="modal-close" onclick="closeModal('confirmModal')">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmModalMessage">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('confirmModal')">Cancel</button>
                <button class="btn btn-danger" id="confirmModalAction">Confirm</button>
            </div>
        </div>
    </div>

    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="editModalTitle">Edit Item</h3>
                <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
            </div>
            <div class="modal-body" id="editModalBody">
                <!-- Dynamic form content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('editModal')">Cancel</button>
                <button class="btn btn-primary" id="editModalSave">Save Changes</button>
            </div>
        </div>
    </div>

    <div id="addModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="addModalTitle">Add New Item</h3>
                <button class="modal-close" onclick="closeModal('addModal')">&times;</button>
            </div>
            <div class="modal-body" id="addModalBody">
                <!-- Dynamic form content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('addModal')">Cancel</button>
                <button class="btn btn-success" id="addModalSave">Add Item</button>
            </div>
        </div>
    </div>

    <script>
        // Application State
        let currentUser = null;
        let currentSection = 'dashboard';
        let isLoading = false;
        let editingItem = null;
        let currentAction = null;

        // Mock Data
        const mockUsers = [
            { id: '1', firstName: 'Super', lastName: 'Admin', email: '<EMAIL>', role: 'super_admin', status: 'active' },
            { id: '2', firstName: 'John', lastName: 'Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
            { id: '3', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', role: 'user', status: 'active' },
            { id: '4', firstName: 'Mike', lastName: 'Johnson', email: '<EMAIL>', role: 'moderator', status: 'active' },
            { id: '5', firstName: 'Sarah', lastName: 'Wilson', email: '<EMAIL>', role: 'user', status: 'inactive' }
        ];

        const mockVendors = [
            { id: '1', businessName: 'Elite Catering', email: '<EMAIL>', status: 'pending', category: 'Catering' },
            { id: '2', businessName: 'Perfect Photos', email: '<EMAIL>', status: 'approved', category: 'Photography' },
            { id: '3', businessName: 'Dream Decorations', email: '<EMAIL>', status: 'pending', category: 'Decoration' },
            { id: '4', businessName: 'Sound Masters', email: '<EMAIL>', status: 'approved', category: 'Audio/Visual' },
            { id: '5', businessName: 'Flower Paradise', email: '<EMAIL>', status: 'rejected', category: 'Florals' },
            { id: '6', businessName: 'Magic Entertainment', email: '<EMAIL>', status: 'approved', category: 'Entertainment' },
            { id: '7', businessName: 'Luxury Transport', email: '<EMAIL>', status: 'pending', category: 'Transportation' }
        ];

        const mockStats = {
            totalUsers: 1247,
            totalVendors: 89,
            totalBookings: 456,
            totalRevenue: 89750
        };

        // Utility Functions
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function setLoading(loading) {
            isLoading = loading;
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn) {
                loginBtn.disabled = loading;
                loginBtn.textContent = loading ? 'Signing In...' : 'Sign In';
            }
        }

        // Modal Functions
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
                editingItem = null;
                currentAction = null;
            }
        }

        // CRUD Functions
        function deleteUser(userId) {
            const user = mockUsers.find(u => u.id === userId);
            if (!user) return;

            document.getElementById('confirmModalTitle').textContent = 'Delete User';
            document.getElementById('confirmModalMessage').textContent =
                `Are you sure you want to delete user "${user.firstName} ${user.lastName}"? This action cannot be undone.`;

            currentAction = () => {
                const index = mockUsers.findIndex(u => u.id === userId);
                if (index > -1) {
                    mockUsers.splice(index, 1);
                    showNotification(`User "${user.firstName} ${user.lastName}" has been deleted successfully.`, 'success');
                    loadSectionContent('users');
                    closeModal('confirmModal');
                }
            };

            document.getElementById('confirmModalAction').onclick = currentAction;
            openModal('confirmModal');
        }

        function editUser(userId) {
            const user = mockUsers.find(u => u.id === userId);
            if (!user) return;

            editingItem = user;
            document.getElementById('editModalTitle').textContent = 'Edit User';
            document.getElementById('editModalBody').innerHTML = `
                <div class="form-group">
                    <label class="form-label">First Name</label>
                    <input type="text" class="form-input" id="editFirstName" value="${user.firstName}">
                </div>
                <div class="form-group">
                    <label class="form-label">Last Name</label>
                    <input type="text" class="form-input" id="editLastName" value="${user.lastName}">
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" id="editEmail" value="${user.email}">
                </div>
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select class="form-select" id="editRole">
                        <option value="user" ${user.role === 'user' ? 'selected' : ''}>User</option>
                        <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                        <option value="moderator" ${user.role === 'moderator' ? 'selected' : ''}>Moderator</option>
                        <option value="super_admin" ${user.role === 'super_admin' ? 'selected' : ''}>Super Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" id="editStatus">
                        <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                        <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                    </select>
                </div>
            `;

            document.getElementById('editModalSave').onclick = () => saveUserEdit(userId);
            openModal('editModal');
        }

        function saveUserEdit(userId) {
            const user = mockUsers.find(u => u.id === userId);
            if (!user) return;

            const firstName = document.getElementById('editFirstName').value.trim();
            const lastName = document.getElementById('editLastName').value.trim();
            const email = document.getElementById('editEmail').value.trim();
            const role = document.getElementById('editRole').value;
            const status = document.getElementById('editStatus').value;

            if (!firstName || !lastName || !email) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Check if email already exists for another user
            const existingUser = mockUsers.find(u => u.email === email && u.id !== userId);
            if (existingUser) {
                showNotification('Email already exists for another user.', 'error');
                return;
            }

            user.firstName = firstName;
            user.lastName = lastName;
            user.email = email;
            user.role = role;
            user.status = status;

            showNotification(`User "${firstName} ${lastName}" has been updated successfully.`, 'success');
            loadSectionContent('users');
            closeModal('editModal');
        }

        function addUser() {
            document.getElementById('addModalTitle').textContent = 'Add New User';
            document.getElementById('addModalBody').innerHTML = `
                <div class="form-group">
                    <label class="form-label">First Name</label>
                    <input type="text" class="form-input" id="addFirstName" placeholder="Enter first name">
                </div>
                <div class="form-group">
                    <label class="form-label">Last Name</label>
                    <input type="text" class="form-input" id="addLastName" placeholder="Enter last name">
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" id="addEmail" placeholder="Enter email address">
                </div>
                <div class="form-group">
                    <label class="form-label">Role</label>
                    <select class="form-select" id="addRole">
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                        <option value="moderator">Moderator</option>
                        <option value="super_admin">Super Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" id="addStatus">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
            `;

            document.getElementById('addModalSave').onclick = saveNewUser;
            openModal('addModal');
        }

        function saveNewUser() {
            const firstName = document.getElementById('addFirstName').value.trim();
            const lastName = document.getElementById('addLastName').value.trim();
            const email = document.getElementById('addEmail').value.trim();
            const role = document.getElementById('addRole').value;
            const status = document.getElementById('addStatus').value;

            if (!firstName || !lastName || !email) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Check if email already exists
            const existingUser = mockUsers.find(u => u.email === email);
            if (existingUser) {
                showNotification('Email already exists.', 'error');
                return;
            }

            const newUser = {
                id: (mockUsers.length + 1).toString(),
                firstName,
                lastName,
                email,
                role,
                status
            };

            mockUsers.push(newUser);
            showNotification(`User "${firstName} ${lastName}" has been added successfully.`, 'success');
            loadSectionContent('users');
            closeModal('addModal');
        }

        // Vendor CRUD Functions
        function deleteVendor(vendorId) {
            const vendor = mockVendors.find(v => v.id === vendorId);
            if (!vendor) return;

            document.getElementById('confirmModalTitle').textContent = 'Delete Vendor';
            document.getElementById('confirmModalMessage').textContent =
                `Are you sure you want to delete vendor "${vendor.businessName}"? This action cannot be undone.`;

            currentAction = () => {
                const index = mockVendors.findIndex(v => v.id === vendorId);
                if (index > -1) {
                    mockVendors.splice(index, 1);
                    showNotification(`Vendor "${vendor.businessName}" has been deleted successfully.`, 'success');
                    loadSectionContent('vendors');
                    closeModal('confirmModal');
                }
            };

            document.getElementById('confirmModalAction').onclick = currentAction;
            openModal('confirmModal');
        }

        function approveVendor(vendorId) {
            const vendor = mockVendors.find(v => v.id === vendorId);
            if (!vendor) return;

            vendor.status = 'approved';
            showNotification(`Vendor "${vendor.businessName}" has been approved successfully.`, 'success');
            loadSectionContent('vendors');
        }

        function rejectVendor(vendorId) {
            const vendor = mockVendors.find(v => v.id === vendorId);
            if (!vendor) return;

            document.getElementById('confirmModalTitle').textContent = 'Reject Vendor';
            document.getElementById('confirmModalMessage').textContent =
                `Are you sure you want to reject vendor "${vendor.businessName}"?`;

            currentAction = () => {
                vendor.status = 'rejected';
                showNotification(`Vendor "${vendor.businessName}" has been rejected.`, 'warning');
                loadSectionContent('vendors');
                closeModal('confirmModal');
            };

            document.getElementById('confirmModalAction').onclick = currentAction;
            openModal('confirmModal');
        }

        function editVendor(vendorId) {
            const vendor = mockVendors.find(v => v.id === vendorId);
            if (!vendor) return;

            editingItem = vendor;
            document.getElementById('editModalTitle').textContent = 'Edit Vendor';
            document.getElementById('editModalBody').innerHTML = `
                <div class="form-group">
                    <label class="form-label">Business Name</label>
                    <input type="text" class="form-input" id="editBusinessName" value="${vendor.businessName}">
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" id="editVendorEmail" value="${vendor.email}">
                </div>
                <div class="form-group">
                    <label class="form-label">Category</label>
                    <select class="form-select" id="editCategory">
                        <option value="Catering" ${vendor.category === 'Catering' ? 'selected' : ''}>Catering</option>
                        <option value="Photography" ${vendor.category === 'Photography' ? 'selected' : ''}>Photography</option>
                        <option value="Decoration" ${vendor.category === 'Decoration' ? 'selected' : ''}>Decoration</option>
                        <option value="Audio/Visual" ${vendor.category === 'Audio/Visual' ? 'selected' : ''}>Audio/Visual</option>
                        <option value="Florals" ${vendor.category === 'Florals' ? 'selected' : ''}>Florals</option>
                        <option value="Entertainment" ${vendor.category === 'Entertainment' ? 'selected' : ''}>Entertainment</option>
                        <option value="Transportation" ${vendor.category === 'Transportation' ? 'selected' : ''}>Transportation</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" id="editVendorStatus">
                        <option value="pending" ${vendor.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="approved" ${vendor.status === 'approved' ? 'selected' : ''}>Approved</option>
                        <option value="rejected" ${vendor.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                    </select>
                </div>
            `;

            document.getElementById('editModalSave').onclick = () => saveVendorEdit(vendorId);
            openModal('editModal');
        }

        function saveVendorEdit(vendorId) {
            const vendor = mockVendors.find(v => v.id === vendorId);
            if (!vendor) return;

            const businessName = document.getElementById('editBusinessName').value.trim();
            const email = document.getElementById('editVendorEmail').value.trim();
            const category = document.getElementById('editCategory').value;
            const status = document.getElementById('editVendorStatus').value;

            if (!businessName || !email) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Check if email already exists for another vendor
            const existingVendor = mockVendors.find(v => v.email === email && v.id !== vendorId);
            if (existingVendor) {
                showNotification('Email already exists for another vendor.', 'error');
                return;
            }

            vendor.businessName = businessName;
            vendor.email = email;
            vendor.category = category;
            vendor.status = status;

            showNotification(`Vendor "${businessName}" has been updated successfully.`, 'success');
            loadSectionContent('vendors');
            closeModal('editModal');
        }

        function addVendor() {
            document.getElementById('addModalTitle').textContent = 'Add New Vendor';
            document.getElementById('addModalBody').innerHTML = `
                <div class="form-group">
                    <label class="form-label">Business Name</label>
                    <input type="text" class="form-input" id="addBusinessName" placeholder="Enter business name">
                </div>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-input" id="addVendorEmail" placeholder="Enter email address">
                </div>
                <div class="form-group">
                    <label class="form-label">Category</label>
                    <select class="form-select" id="addCategory">
                        <option value="Catering">Catering</option>
                        <option value="Photography">Photography</option>
                        <option value="Decoration">Decoration</option>
                        <option value="Audio/Visual">Audio/Visual</option>
                        <option value="Florals">Florals</option>
                        <option value="Entertainment">Entertainment</option>
                        <option value="Transportation">Transportation</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" id="addVendorStatus">
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                    </select>
                </div>
            `;

            document.getElementById('addModalSave').onclick = saveNewVendor;
            openModal('addModal');
        }

        function saveNewVendor() {
            const businessName = document.getElementById('addBusinessName').value.trim();
            const email = document.getElementById('addVendorEmail').value.trim();
            const category = document.getElementById('addCategory').value;
            const status = document.getElementById('addVendorStatus').value;

            if (!businessName || !email) {
                showNotification('Please fill in all required fields.', 'error');
                return;
            }

            // Check if email already exists
            const existingVendor = mockVendors.find(v => v.email === email);
            if (existingVendor) {
                showNotification('Email already exists.', 'error');
                return;
            }

            const newVendor = {
                id: (mockVendors.length + 1).toString(),
                businessName,
                email,
                category,
                status
            };

            mockVendors.push(newVendor);
            showNotification(`Vendor "${businessName}" has been added successfully.`, 'success');
            loadSectionContent('vendors');
            closeModal('addModal');
        }

        // Authentication Functions
        function showLogin() {
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('registerForm').classList.add('hidden');
        }

        function showRegister() {
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('registerForm').classList.remove('hidden');
        }

        function handleLogin() {
            const email = document.getElementById('loginEmail').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!email || !password) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            setLoading(true);

            // Simulate API call
            setTimeout(() => {
                if (email === '<EMAIL>' && password === 'SuperAdmin123!') {
                    currentUser = {
                        firstName: 'Super',
                        lastName: 'Admin',
                        role: 'super_admin',
                        email: email
                    };
                    showDashboard();
                    showNotification('Welcome to EventEase Admin Dashboard!', 'success');
                } else {
                    showNotification('Invalid email or password. Please try again.', 'error');
                }
                setLoading(false);
            }, 1000);
        }

        function handleRegister() {
            const firstName = document.getElementById('regFirstName').value.trim();
            const lastName = document.getElementById('regLastName').value.trim();
            const email = document.getElementById('regEmail').value.trim();
            const password = document.getElementById('regPassword').value.trim();
            const adminCode = document.getElementById('regAdminCode').value.trim();

            if (!firstName || !lastName || !email || !password) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            if (adminCode !== 'ADMIN2024') {
                showNotification('Invalid admin registration code', 'error');
                return;
            }

            // Check if email already exists
            if (mockUsers.find(user => user.email === email)) {
                showNotification('Email already exists', 'error');
                return;
            }

            currentUser = { firstName, lastName, role: 'admin', email };
            showDashboard();
            showNotification('Account created successfully! Welcome to EventEase Admin.', 'success');
        }

        function handleLogout() {
            currentUser = null;
            document.getElementById('authContainer').style.display = 'flex';
            document.getElementById('dashboardContainer').classList.remove('active');
            showNotification('Logged out successfully', 'success');
        }

        function showDashboard() {
            document.getElementById('authContainer').style.display = 'none';
            document.getElementById('dashboardContainer').classList.add('active');

            // Update profile info
            document.getElementById('profileName').textContent = `${currentUser.firstName} ${currentUser.lastName}`;
            document.getElementById('profileRole').textContent = currentUser.role.replace('_', ' ').toUpperCase();

            // Load dashboard content
            showSection('dashboard', document.querySelector('.menu-item.active'));
        }

        // Navigation Functions
        function showSection(section, element) {
            currentSection = section;

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
            if (element) {
                element.classList.add('active');
            }

            // Update content title and breadcrumb
            const titles = {
                'dashboard': 'Dashboard',
                'users': 'User Management',
                'vendors': 'Vendor Management',
                'events': 'Events Management',
                'bookings': 'Bookings Management',
                'analytics': 'Analytics & Reports',
                'settings': 'System Settings',
                'reports': 'Reports'
            };

            document.getElementById('contentTitle').textContent = titles[section];
            document.getElementById('breadcrumbActive').textContent = titles[section];

            // Load section content
            loadSectionContent(section);
        }

        function loadSectionContent(section) {
            const contentArea = document.getElementById('contentArea');
            contentArea.innerHTML = '<div class="loading">Loading...</div>';

            // Simulate loading delay
            setTimeout(() => {
                switch(section) {
                    case 'dashboard':
                        contentArea.innerHTML = getDashboardContent();
                        break;
                    case 'users':
                        contentArea.innerHTML = getUsersContent();
                        break;
                    case 'vendors':
                        contentArea.innerHTML = getVendorsContent();
                        break;
                    case 'events':
                        contentArea.innerHTML = getEventsContent();
                        break;
                    case 'bookings':
                        contentArea.innerHTML = getBookingsContent();
                        break;
                    case 'analytics':
                        contentArea.innerHTML = getAnalyticsContent();
                        break;
                    case 'settings':
                        contentArea.innerHTML = getSettingsContent();
                        break;
                    case 'reports':
                        contentArea.innerHTML = getReportsContent();
                        break;
                    default:
                        contentArea.innerHTML = getDashboardContent();
                }
            }, 300);
        }

        // Content Generation Functions
        function getDashboardContent() {
            return `
                <div class="stats-grid">
                    <div class="stat-card users">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalUsers.toLocaleString()}</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <i class="stat-icon fas fa-users"></i>
                    </div>
                    <div class="stat-card vendors">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalVendors}</div>
                            <div class="stat-label">Total Vendors</div>
                        </div>
                        <i class="stat-icon fas fa-building"></i>
                    </div>
                    <div class="stat-card bookings">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalBookings}</div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                        <i class="stat-icon fas fa-calendar-check"></i>
                    </div>
                    <div class="stat-card revenue">
                        <div class="stat-content">
                            <div class="stat-number">$${mockStats.totalRevenue.toLocaleString()}</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <i class="stat-icon fas fa-dollar-sign"></i>
                    </div>
                </div>

                <div style="margin-bottom: 30px;">
                    <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--secondary-900); margin-bottom: var(--space-5); display: flex; align-items: center; gap: var(--space-3);"><i class="fas fa-chart-line" style="color: var(--primary-600);"></i> Recent Activity</h3>
                    <div class="data-list">
                        <div class="data-item">
                            <div class="data-avatar" style="background: linear-gradient(135deg, var(--success-500), var(--success-600));"><i class="fas fa-user-plus"></i></div>
                            <div class="data-info">
                                <div class="data-name">New user registered: John Doe</div>
                                <div class="data-email">2 minutes ago</div>
                            </div>
                        </div>
                        <div class="data-item">
                            <div class="data-avatar" style="background: linear-gradient(135deg, var(--warning-500), var(--warning-600));"><i class="fas fa-building"></i></div>
                            <div class="data-info">
                                <div class="data-name">Vendor application submitted: Elite Catering</div>
                                <div class="data-email">15 minutes ago</div>
                            </div>
                        </div>
                        <div class="data-item">
                            <div class="data-avatar" style="background: linear-gradient(135deg, var(--primary-500), var(--primary-600));"><i class="fas fa-calendar-plus"></i></div>
                            <div class="data-info">
                                <div class="data-name">New booking created for Wedding Event</div>
                                <div class="data-email">1 hour ago</div>
                            </div>
                        </div>
                        <div class="data-item">
                            <div class="data-avatar" style="background: linear-gradient(135deg, #10b981, #059669);"><i class="fas fa-dollar-sign"></i></div>
                            <div class="data-info">
                                <div class="data-name">Payment received: $2,500</div>
                                <div class="data-email">3 hours ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getUsersContent() {
            return `
                <div style="margin-bottom: var(--space-5); display: flex; justify-content: space-between; align-items: center;">
                    <p style="font-size: 1rem; color: var(--secondary-600); font-weight: 500;">Manage all users and their permissions</p>
                    <button class="btn btn-primary" onclick="addUser()">
                        <i class="fas fa-plus"></i> Add User
                    </button>
                </div>

                <div class="data-list">
                    <h3 style="font-size: 1.25rem; font-weight: 700; color: var(--secondary-900); margin-bottom: var(--space-5); display: flex; align-items: center; gap: var(--space-3);"><i class="fas fa-users" style="color: var(--primary-600);"></i> All Users (${mockUsers.length})</h3>
                    ${mockUsers.map(user => `
                        <div class="data-item">
                            <div class="data-avatar">${user.firstName[0]}${user.lastName[0]}</div>
                            <div class="data-info">
                                <div class="data-name">${user.firstName} ${user.lastName}</div>
                                <div class="data-email">${user.email}</div>
                                <div class="data-role">${user.role.replace('_', ' ')}</div>
                            </div>
                            <div class="status-badge status-${user.status}">${user.status}</div>
                            <div class="action-buttons">
                                <button class="btn btn-secondary" onclick="editUser('${user.id}')" title="Edit User">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger" onclick="deleteUser('${user.id}')" title="Delete User">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function getVendorsContent() {
            return `
                <div style="margin-bottom: var(--space-5); display: flex; justify-content: space-between; align-items: center;">
                    <p style="font-size: 1rem; color: var(--secondary-600); font-weight: 500;">Review and manage vendor applications</p>
                    <button class="btn btn-primary" onclick="addVendor()">
                        <i class="fas fa-plus"></i> Add Vendor
                    </button>
                </div>

                <div class="data-list">
                    <h3 style="font-size: 1.25rem; font-weight: 700; color: var(--secondary-900); margin-bottom: var(--space-5); display: flex; align-items: center; gap: var(--space-3);"><i class="fas fa-building" style="color: var(--primary-600);"></i> All Vendors (${mockVendors.length})</h3>
                    ${mockVendors.map(vendor => `
                        <div class="data-item">
                            <div class="data-avatar">${vendor.businessName[0]}</div>
                            <div class="data-info">
                                <div class="data-name">${vendor.businessName}</div>
                                <div class="data-email">${vendor.email}</div>
                                <div class="data-role">${vendor.category}</div>
                            </div>
                            <div class="status-badge status-${vendor.status}">${vendor.status}</div>
                            <div class="action-buttons">
                                ${vendor.status === 'pending' ? `
                                    <button class="btn btn-success" onclick="approveVendor('${vendor.id}')" title="Approve Vendor">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-warning" onclick="rejectVendor('${vendor.id}')" title="Reject Vendor">
                                        <i class="fas fa-times"></i>
                                    </button>
                                ` : ''}
                                <button class="btn btn-secondary" onclick="editVendor('${vendor.id}')" title="Edit Vendor">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-danger" onclick="deleteVendor('${vendor.id}')" title="Delete Vendor">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function getEventsContent() {
            return `
                <div style="margin-bottom: var(--space-5);">
                    <p style="font-size: 1rem; color: var(--secondary-600); margin-bottom: var(--space-8); font-weight: 500;">Manage all events and their details</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: var(--space-16) var(--space-10); color: var(--secondary-500);">
                        <i class="fas fa-calendar-star" style="font-size: 4rem; margin-bottom: var(--space-5); color: var(--primary-400);"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: var(--space-3); color: var(--secondary-800); font-weight: 700;">Events Management</h3>
                        <p style="font-size: 1rem; margin-bottom: var(--space-5); font-weight: 500;">Events management functionality will be implemented here</p>
                        <div style="margin-top: var(--space-5); padding: var(--space-4); background: var(--primary-50); border-radius: var(--radius-xl); color: var(--secondary-700); border: 1px solid var(--primary-200);">
                            <strong style="color: var(--primary-700);">Coming Soon:</strong> Create, edit, and manage events with full calendar integration
                        </div>
                    </div>
                </div>
            `;
        }

        function getBookingsContent() {
            return `
                <div style="margin-bottom: var(--space-5);">
                    <p style="font-size: 1rem; color: var(--secondary-600); margin-bottom: var(--space-8); font-weight: 500;">View and manage all bookings</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: var(--space-16) var(--space-10); color: var(--secondary-500);">
                        <i class="fas fa-calendar-check" style="font-size: 4rem; margin-bottom: var(--space-5); color: var(--primary-400);"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: var(--space-3); color: var(--secondary-800); font-weight: 700;">Bookings Management</h3>
                        <p style="font-size: 1rem; margin-bottom: var(--space-5); font-weight: 500;">Bookings management functionality will be implemented here</p>
                        <div style="margin-top: var(--space-5); padding: var(--space-4); background: var(--primary-50); border-radius: var(--radius-xl); color: var(--secondary-700); border: 1px solid var(--primary-200);">
                            <strong style="color: var(--primary-700);">Coming Soon:</strong> Track bookings, payments, and customer communications
                        </div>
                    </div>
                </div>
            `;
        }

        function getAnalyticsContent() {
            return `
                <div style="margin-bottom: var(--space-5);">
                    <p style="font-size: 1rem; color: var(--secondary-600); margin-bottom: var(--space-8); font-weight: 500;">View detailed analytics and generate reports</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: var(--space-16) var(--space-10); color: var(--secondary-500);">
                        <i class="fas fa-chart-bar" style="font-size: 4rem; margin-bottom: var(--space-5); color: var(--primary-400);"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: var(--space-3); color: var(--secondary-800); font-weight: 700;">Analytics & Reports</h3>
                        <p style="font-size: 1rem; margin-bottom: var(--space-5); font-weight: 500;">Advanced analytics functionality will be implemented here</p>
                        <div style="margin-top: var(--space-5); padding: var(--space-4); background: var(--primary-50); border-radius: var(--radius-xl); color: var(--secondary-700); border: 1px solid var(--primary-200);">
                            <strong style="color: var(--primary-700);">Coming Soon:</strong> Interactive charts, revenue analytics, and custom reports
                        </div>
                    </div>
                </div>
            `;
        }

        function getSettingsContent() {
            return `
                <div style="margin-bottom: var(--space-5);">
                    <p style="font-size: 1rem; color: var(--secondary-600); margin-bottom: var(--space-8); font-weight: 500;">Configure system preferences and settings</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: var(--space-16) var(--space-10); color: var(--secondary-500);">
                        <i class="fas fa-cog" style="font-size: 4rem; margin-bottom: var(--space-5); color: var(--primary-400);"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: var(--space-3); color: var(--secondary-800); font-weight: 700;">System Settings</h3>
                        <p style="font-size: 1rem; margin-bottom: var(--space-5); font-weight: 500;">System configuration functionality will be implemented here</p>
                        <div style="margin-top: var(--space-5); padding: var(--space-4); background: var(--primary-50); border-radius: var(--radius-xl); color: var(--secondary-700); border: 1px solid var(--primary-200);">
                            <strong style="color: var(--primary-700);">Coming Soon:</strong> User preferences, system configuration, and security settings
                        </div>
                    </div>
                </div>
            `;
        }

        function getReportsContent() {
            return `
                <div style="margin-bottom: var(--space-5);">
                    <p style="font-size: 1rem; color: var(--secondary-600); margin-bottom: var(--space-8); font-weight: 500;">Generate and download various reports</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: var(--space-16) var(--space-10); color: var(--secondary-500);">
                        <i class="fas fa-file-alt" style="font-size: 4rem; margin-bottom: var(--space-5); color: var(--primary-400);"></i>
                        <h3 style="font-size: 1.5rem; margin-bottom: var(--space-3); color: var(--secondary-800); font-weight: 700;">Reports</h3>
                        <p style="font-size: 1rem; margin-bottom: var(--space-5); font-weight: 500;">Report generation functionality will be implemented here</p>
                        <div style="margin-top: var(--space-5); padding: var(--space-4); background: var(--primary-50); border-radius: var(--radius-xl); color: var(--secondary-700); border: 1px solid var(--primary-200);">
                            <strong style="color: var(--primary-700);">Coming Soon:</strong> PDF reports, data exports, and scheduled reporting
                        </div>
                    </div>
                </div>
            `;
        }

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 EventEase Admin Dashboard loaded successfully!');
            console.log('📧 Default admin: <EMAIL>');
            console.log('🔑 Default password: SuperAdmin123!');

            // Add Enter key support for login
            document.getElementById('loginPassword').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin();
                }
            });

            showNotification('EventEase Admin Dashboard loaded successfully!', 'success');
        });
    </script>
</body>
</html>
