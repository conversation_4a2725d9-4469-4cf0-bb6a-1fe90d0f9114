<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventEase Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #2d3748;
        }

        /* Authentication Styles */
        .auth-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            padding: 40px;
            width: 100%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .auth-logo {
            font-size: 36px;
            font-weight: 800;
            color: #1a202c;
            margin-bottom: 8px;
            letter-spacing: -1px;
        }

        .auth-subtitle {
            font-size: 18px;
            color: #718096;
            font-weight: 500;
        }

        .input-group {
            margin-bottom: 24px;
        }

        .input-label {
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            display: block;
        }

        .auth-input {
            width: 100%;
            height: 56px;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 0 20px;
            font-size: 16px;
            background: #ffffff;
            color: #2d3748;
            font-weight: 500;
            transition: border-color 0.3s ease;
        }

        .auth-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .auth-button {
            width: 100%;
            background: #667eea;
            height: 56px;
            border: none;
            border-radius: 16px;
            color: white;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            margin-top: 16px;
            margin-bottom: 24px;
            transition: background 0.3s ease;
        }

        .auth-button:hover {
            background: #5a67d8;
        }

        .auth-switch {
            text-align: center;
            margin-top: 16px;
        }

        .auth-switch-text {
            font-size: 16px;
            color: #718096;
        }

        .auth-switch-link {
            color: #667eea;
            font-weight: 700;
            cursor: pointer;
            text-decoration: none;
        }

        /* Dashboard Styles */
        .dashboard-container {
            display: none;
            min-height: 100vh;
        }

        .dashboard-container.active {
            display: flex;
            flex-direction: column;
        }

        .top-bar {
            height: 80px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            padding: 0 30px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
        }

        .logo-section {
            flex: 1;
        }

        .top-logo {
            font-size: 28px;
            font-weight: 800;
            color: #1a202c;
        }

        .top-subtitle {
            font-size: 14px;
            color: #718096;
            margin-top: 2px;
        }

        .profile-section {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .profile-name {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
        }

        .profile-role {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
        }

        .logout-btn {
            background: #e53e3e;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }

        .body-container {
            flex: 1;
            display: flex;
        }

        .sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 20px 16px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            margin-bottom: 8px;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            background: #f7fafc;
        }

        .menu-item.active {
            background: #667eea;
            color: white;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .menu-icon {
            font-size: 20px;
            margin-right: 16px;
            width: 28px;
        }

        .menu-text {
            font-size: 16px;
            font-weight: 600;
        }

        .main-content {
            flex: 1;
            background: #f8fafc;
        }

        .content-header {
            background: white;
            padding: 24px 30px;
            border-bottom: 1px solid #e2e8f0;
        }

        .content-title {
            font-size: 32px;
            font-weight: 800;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .content-area {
            padding: 30px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            border-radius: 20px;
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .stat-card.users { background: linear-gradient(135deg, #667eea, #764ba2); color: white; }
        .stat-card.vendors { background: linear-gradient(135deg, #f093fb, #f5576c); color: white; }
        .stat-card.bookings { background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; }
        .stat-card.revenue { background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 800;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
            font-weight: 600;
        }

        .stat-icon {
            font-size: 32px;
            opacity: 0.8;
        }

        /* Data Lists */
        .data-list {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
        }

        .data-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .data-avatar {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            margin-right: 16px;
        }

        .data-info {
            flex: 1;
        }

        .data-name {
            font-size: 16px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .data-email {
            font-size: 14px;
            color: #718096;
        }

        .data-role {
            font-size: 12px;
            color: #667eea;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending { background: #fed7d7; color: #c53030; }
        .status-approved { background: #c6f6d5; color: #2f855a; }
        .status-active { background: #bee3f8; color: #2b6cb0; }

        .hidden {
            display: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: fixed;
                top: 80px;
                left: -100%;
                height: calc(100vh - 80px);
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .sidebar.open {
                left: 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .auth-card {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <!-- Authentication Screen -->
    <div id="authContainer" class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo">EventEase</div>
                <div class="auth-subtitle">Admin Dashboard</div>
            </div>

            <div id="loginForm">
                <div class="input-group">
                    <label class="input-label">Email Address</label>
                    <input type="email" class="auth-input" id="loginEmail" value="<EMAIL>" placeholder="Enter your email">
                </div>

                <div class="input-group">
                    <label class="input-label">Password</label>
                    <input type="password" class="auth-input" id="loginPassword" value="SuperAdmin123!" placeholder="Enter your password">
                </div>

                <button class="auth-button" onclick="handleLogin()">Sign In</button>

                <div class="auth-switch">
                    <span class="auth-switch-text">
                        Don't have an account?
                        <a href="#" class="auth-switch-link" onclick="showRegister()">Register here</a>
                    </span>
                </div>
            </div>

            <div id="registerForm" class="hidden">
                <div class="input-group">
                    <label class="input-label">First Name</label>
                    <input type="text" class="auth-input" id="regFirstName" placeholder="First name">
                </div>

                <div class="input-group">
                    <label class="input-label">Last Name</label>
                    <input type="text" class="auth-input" id="regLastName" placeholder="Last name">
                </div>

                <div class="input-group">
                    <label class="input-label">Email Address</label>
                    <input type="email" class="auth-input" id="regEmail" placeholder="Enter your email">
                </div>

                <div class="input-group">
                    <label class="input-label">Password</label>
                    <input type="password" class="auth-input" id="regPassword" placeholder="Enter your password">
                </div>

                <div class="input-group">
                    <label class="input-label">Admin Registration Code</label>
                    <input type="text" class="auth-input" id="regAdminCode" value="ADMIN2024" placeholder="Enter admin code">
                </div>

                <button class="auth-button" onclick="handleRegister()">Create Account</button>

                <div class="auth-switch">
                    <span class="auth-switch-text">
                        Already have an account?
                        <a href="#" class="auth-switch-link" onclick="showLogin()">Sign in here</a>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Screen -->
    <div id="dashboardContainer" class="dashboard-container">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="logo-section">
                <div class="top-logo">EventEase</div>
                <div class="top-subtitle">Admin Dashboard</div>
            </div>

            <div class="profile-section">
                <div>
                    <div class="profile-name" id="profileName">Super Admin</div>
                    <div class="profile-role" id="profileRole">Administrator</div>
                </div>
                <button class="logout-btn" onclick="handleLogout()">Logout</button>
            </div>
        </div>

        <!-- Body Container -->
        <div class="body-container">
            <!-- Sidebar -->
            <div class="sidebar" id="sidebar">
                <div class="menu-item active" onclick="showSection('dashboard')">
                    <span class="menu-icon">📊</span>
                    <span class="menu-text">Dashboard</span>
                </div>
                <div class="menu-item" onclick="showSection('users')">
                    <span class="menu-icon">👥</span>
                    <span class="menu-text">User Management</span>
                </div>
                <div class="menu-item" onclick="showSection('vendors')">
                    <span class="menu-icon">🏢</span>
                    <span class="menu-text">Vendor Management</span>
                </div>
                <div class="menu-item" onclick="showSection('events')">
                    <span class="menu-icon">🎉</span>
                    <span class="menu-text">Events</span>
                </div>
                <div class="menu-item" onclick="showSection('bookings')">
                    <span class="menu-icon">📅</span>
                    <span class="menu-text">Bookings</span>
                </div>
                <div class="menu-item" onclick="showSection('analytics')">
                    <span class="menu-icon">📈</span>
                    <span class="menu-text">Analytics</span>
                </div>
                <div class="menu-item" onclick="showSection('settings')">
                    <span class="menu-icon">⚙️</span>
                    <span class="menu-text">Settings</span>
                </div>
                <div class="menu-item" onclick="showSection('reports')">
                    <span class="menu-icon">📋</span>
                    <span class="menu-text">Reports</span>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="content-header">
                    <div class="content-title" id="contentTitle">Dashboard</div>
                </div>

                <div class="content-area" id="contentArea">
                    <!-- Dashboard content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mock data
        const mockUsers = [
            { id: '1', firstName: 'Super', lastName: 'Admin', email: '<EMAIL>', role: 'super_admin', status: 'active' },
            { id: '2', firstName: 'John', lastName: 'Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
            { id: '3', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', role: 'user', status: 'active' },
            { id: '4', firstName: 'Mike', lastName: 'Johnson', email: '<EMAIL>', role: 'moderator', status: 'active' },
            { id: '5', firstName: 'Sarah', lastName: 'Wilson', email: '<EMAIL>', role: 'user', status: 'inactive' }
        ];

        const mockVendors = [
            { id: '1', businessName: 'Elite Catering', email: '<EMAIL>', status: 'pending', category: 'Catering' },
            { id: '2', businessName: 'Perfect Photos', email: '<EMAIL>', status: 'approved', category: 'Photography' },
            { id: '3', businessName: 'Dream Decorations', email: '<EMAIL>', status: 'pending', category: 'Decoration' },
            { id: '4', businessName: 'Sound Masters', email: '<EMAIL>', status: 'approved', category: 'Audio/Visual' },
            { id: '5', businessName: 'Flower Paradise', email: '<EMAIL>', status: 'pending', category: 'Florals' }
        ];

        const mockStats = {
            totalUsers: 1247,
            totalVendors: 89,
            totalBookings: 456,
            totalRevenue: 89750
        };

        let currentUser = null;
        let currentSection = 'dashboard';

        // Authentication functions
        function showLogin() {
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('registerForm').classList.add('hidden');
        }

        function showRegister() {
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('registerForm').classList.remove('hidden');
        }

        function handleLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (email === '<EMAIL>' && password === 'SuperAdmin123!') {
                currentUser = { firstName: 'Super', lastName: 'Admin', role: 'super_admin' };
                showDashboard();
                showNotification('Login successful! Welcome to EventEase Admin.', 'success');
            } else {
                showNotification('Invalid email or password. Please try again.', 'error');
            }
        }

        function handleRegister() {
            const firstName = document.getElementById('regFirstName').value;
            const lastName = document.getElementById('regLastName').value;
            const email = document.getElementById('regEmail').value;
            const password = document.getElementById('regPassword').value;
            const adminCode = document.getElementById('regAdminCode').value;

            if (!firstName || !lastName || !email || !password) {
                showNotification('Please fill in all fields.', 'error');
                return;
            }

            if (adminCode !== 'ADMIN2024') {
                showNotification('Invalid admin registration code.', 'error');
                return;
            }

            currentUser = { firstName, lastName, role: 'admin' };
            showDashboard();
            showNotification('Account created successfully! Welcome to EventEase Admin.', 'success');
        }

        function handleLogout() {
            currentUser = null;
            document.getElementById('authContainer').style.display = 'flex';
            document.getElementById('dashboardContainer').classList.remove('active');
            showNotification('Logged out successfully.', 'success');
        }

        function showDashboard() {
            document.getElementById('authContainer').style.display = 'none';
            document.getElementById('dashboardContainer').classList.add('active');

            // Update profile info
            document.getElementById('profileName').textContent = `${currentUser.firstName} ${currentUser.lastName}`;
            document.getElementById('profileRole').textContent = currentUser.role.replace('_', ' ').toUpperCase();

            // Show dashboard content
            showSection('dashboard');
        }

        // Navigation functions
        function showSection(section) {
            currentSection = section;

            // Update active menu item
            document.querySelectorAll('.menu-item').forEach(item => item.classList.remove('active'));
            event.target.closest('.menu-item').classList.add('active');

            // Update content title
            const titles = {
                'dashboard': 'Dashboard',
                'users': 'User Management',
                'vendors': 'Vendor Management',
                'events': 'Events Management',
                'bookings': 'Bookings Management',
                'analytics': 'Analytics & Reports',
                'settings': 'System Settings',
                'reports': 'Reports'
            };

            document.getElementById('contentTitle').textContent = titles[section];

            // Load section content
            loadSectionContent(section);
        }

        function loadSectionContent(section) {
            const contentArea = document.getElementById('contentArea');

            switch(section) {
                case 'dashboard':
                    contentArea.innerHTML = getDashboardContent();
                    break;
                case 'users':
                    contentArea.innerHTML = getUsersContent();
                    break;
                case 'vendors':
                    contentArea.innerHTML = getVendorsContent();
                    break;
                case 'events':
                    contentArea.innerHTML = getEventsContent();
                    break;
                case 'bookings':
                    contentArea.innerHTML = getBookingsContent();
                    break;
                case 'analytics':
                    contentArea.innerHTML = getAnalyticsContent();
                    break;
                case 'settings':
                    contentArea.innerHTML = getSettingsContent();
                    break;
                case 'reports':
                    contentArea.innerHTML = getReportsContent();
                    break;
            }
        }

        function getDashboardContent() {
            return `
                <div class="stats-grid">
                    <div class="stat-card users">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalUsers.toLocaleString()}</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-icon">👥</div>
                    </div>
                    <div class="stat-card vendors">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalVendors}</div>
                            <div class="stat-label">Total Vendors</div>
                        </div>
                        <div class="stat-icon">🏢</div>
                    </div>
                    <div class="stat-card bookings">
                        <div class="stat-content">
                            <div class="stat-number">${mockStats.totalBookings}</div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                        <div class="stat-icon">📅</div>
                    </div>
                    <div class="stat-card revenue">
                        <div class="stat-content">
                            <div class="stat-number">$${mockStats.totalRevenue.toLocaleString()}</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <div class="stat-icon">💰</div>
                    </div>
                </div>

                <div style="margin-bottom: 30px;">
                    <h3 style="font-size: 24px; font-weight: 700; color: #1a202c; margin-bottom: 20px;">Recent Activity</h3>
                    <div class="data-list">
                        <div class="data-item">
                            <div class="data-avatar">👤</div>
                            <div class="data-info">
                                <div class="data-name">New user registered: John Doe</div>
                                <div class="data-email">2 minutes ago</div>
                            </div>
                        </div>
                        <div class="data-item">
                            <div class="data-avatar">🏢</div>
                            <div class="data-info">
                                <div class="data-name">Vendor application submitted: Elite Catering</div>
                                <div class="data-email">15 minutes ago</div>
                            </div>
                        </div>
                        <div class="data-item">
                            <div class="data-avatar">📅</div>
                            <div class="data-info">
                                <div class="data-name">New booking created for Wedding Event</div>
                                <div class="data-email">1 hour ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getUsersContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">Manage all users and their permissions</p>
                </div>

                <div class="data-list">
                    ${mockUsers.map(user => `
                        <div class="data-item">
                            <div class="data-avatar">${user.firstName[0]}${user.lastName[0]}</div>
                            <div class="data-info">
                                <div class="data-name">${user.firstName} ${user.lastName}</div>
                                <div class="data-email">${user.email}</div>
                                <div class="data-role">${user.role.replace('_', ' ')}</div>
                            </div>
                            <div class="status-badge status-${user.status}">${user.status}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function getVendorsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">Review and manage vendor applications</p>
                </div>

                <div class="data-list">
                    ${mockVendors.map(vendor => `
                        <div class="data-item">
                            <div class="data-avatar">${vendor.businessName[0]}</div>
                            <div class="data-info">
                                <div class="data-name">${vendor.businessName}</div>
                                <div class="data-email">${vendor.email}</div>
                                <div class="data-role">${vendor.category}</div>
                            </div>
                            <div class="status-badge status-${vendor.status}">${vendor.status}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function getEventsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">Manage all events and their details</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🎉</div>
                        <h3 style="font-size: 20px; margin-bottom: 8px;">Events Management</h3>
                        <p>Events management functionality will be implemented here</p>
                    </div>
                </div>
            `;
        }

        function getBookingsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">View and manage all bookings</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 16px;">📅</div>
                        <h3 style="font-size: 20px; margin-bottom: 8px;">Bookings Management</h3>
                        <p>Bookings management functionality will be implemented here</p>
                    </div>
                </div>
            `;
        }

        function getAnalyticsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">View detailed analytics and generate reports</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 16px;">📈</div>
                        <h3 style="font-size: 20px; margin-bottom: 8px;">Analytics & Reports</h3>
                        <p>Advanced analytics functionality will be implemented here</p>
                    </div>
                </div>
            `;
        }

        function getSettingsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">Configure system preferences and settings</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 16px;">⚙️</div>
                        <h3 style="font-size: 20px; margin-bottom: 8px;">System Settings</h3>
                        <p>System configuration functionality will be implemented here</p>
                    </div>
                </div>
            `;
        }

        function getReportsContent() {
            return `
                <div style="margin-bottom: 20px;">
                    <p style="font-size: 16px; color: #718096; margin-bottom: 30px;">Generate and download various reports</p>
                </div>

                <div class="data-list">
                    <div style="text-align: center; padding: 40px; color: #718096;">
                        <div style="font-size: 48px; margin-bottom: 16px;">📋</div>
                        <h3 style="font-size: 20px; margin-bottom: 8px;">Reports</h3>
                        <p>Report generation functionality will be implemented here</p>
                    </div>
                </div>
            `;
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#667eea'};
                color: white;
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                font-weight: 600;
                max-width: 400px;
                animation: slideIn 0.3s ease;
            `;

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 EventEase Admin Dashboard loaded successfully!');
            console.log('📧 Default admin: <EMAIL>');
            console.log('🔑 Default password: SuperAdmin123!');
        });
    </script>
</body>
</html>
