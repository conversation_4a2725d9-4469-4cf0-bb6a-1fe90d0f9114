const express = require('express');
const router = express.Router();
const {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  updateUserPassword,
  deleteUser,
  getUserStats
} = require('../controllers/userController');

// @route   GET /api/users
// @desc    Get all users with pagination and filtering
// @access  Private (Admin)
router.get('/', getAllUsers);

// @route   GET /api/users/stats
// @desc    Get user statistics
// @access  Private (Admin)
router.get('/stats', getUserStats);

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Private (Admin)
router.get('/:id', getUserById);

// @route   POST /api/users
// @desc    Create new user
// @access  Private (Admin)
router.post('/', createUser);

// @route   PUT /api/users/:id
// @desc    Update user
// @access  Private (Admin)
router.put('/:id', updateUser);

// @route   PUT /api/users/:id/password
// @desc    Update user password
// @access  Private (Admin)
router.put('/:id/password', updateUserPassword);

// @route   DELETE /api/users/:id
// @desc    Delete user (soft delete by default)
// @access  Private (Admin)
router.delete('/:id', deleteUser);

module.exports = router;
