const bcrypt = require('bcryptjs');
const db = require('../mockDb');

// User model for mock database
class User {
  constructor(data) {
    this._id = data._id || Date.now().toString();
    this.name = data.name;
    this.email = data.email;
    this.phone = data.phone || '';
    this.role = data.role || 'user';
    this.status = data.status || 'active';
    this.password = data.password || '';
    this.lastLogin = data.lastLogin || null;
    this.registeredOn = data.registeredOn || new Date().toISOString();
  }

  // Save user to database
  async save() {
    // Check if user already exists
    const existingUser = db.users.find(
      user => user.email === this.email
    );
    
    if (existingUser) {
      throw new Error('User already exists');
    }
    
    // Hash password if provided and not already hashed
    if (this.password && !this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Add to database
    db.users.push(this);
    return this;
  }

  // Update user
  async update() {
    const index = db.users.findIndex(user => user._id === this._id);
    
    if (index === -1) {
      throw new Error('User not found');
    }
    
    // Hash password if changed
    if (this.password && !this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Update in database
    db.users[index] = this;
    return this;
  }

  // Find user by ID
  static async findById(id) {
    const user = db.users.find(user => user._id === id);
    
    if (!user) {
      return null;
    }
    
    return new User(user);
  }

  // Find user by email
  static async findByEmail(email) {
    const user = db.users.find(user => user.email === email);
    
    if (!user) {
      return null;
    }
    
    return new User(user);
  }

  // Find all users
  static async findAll() {
    return db.users.map(user => new User(user));
  }

  // Delete user
  static async deleteById(id) {
    const index = db.users.findIndex(user => user._id === id);
    
    if (index === -1) {
      throw new Error('User not found');
    }
    
    // Remove from database
    db.users.splice(index, 1);
    return true;
  }

  // Select fields to return
  select(fields) {
    if (fields === '-password') {
      const { password, ...userWithoutPassword } = this;
      return userWithoutPassword;
    }
    return this;
  }
}

module.exports = User;
