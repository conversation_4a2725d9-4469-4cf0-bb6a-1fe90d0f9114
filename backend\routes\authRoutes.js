const express = require('express');
const router = express.Router();
const {
  register,
  login,
  getProfile,
  logout,
  verifyToken
} = require('../controllers/authController');

// @route   POST /api/auth/register
// @desc    Register new admin user
// @access  Public (with admin code)
router.post('/register', register);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', login);

// @route   GET /api/auth/profile
// @desc    Get current user profile
// @access  Private
router.get('/profile', verifyToken, getProfile);

// @route   POST /api/auth/logout
// @desc    Logout user
// @access  Private
router.post('/logout', verifyToken, logout);

// @route   GET /api/auth/verify
// @desc    Verify token validity
// @access  Private
router.get('/verify', verifyToken, (req, res) => {
  res.json({
    success: true,
    message: 'Token is valid',
    data: {
      user: req.user
    }
  });
});

module.exports = router;
