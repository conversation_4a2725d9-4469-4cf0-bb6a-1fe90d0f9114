import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, SafeAreaView } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const AdminScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState('Dashboard');

  const renderContent = () => {
    switch (activeSection) {
      case 'Dashboard':
        return <Text style={styles.contentText}>Dashboard statistics and overview</Text>;
      case 'User Management':
        return <Text style={styles.contentText}>User management interface</Text>;
      case 'Events':
        return <Text style={styles.contentText}>Vendor management</Text>;
      case 'Review & Categories':
        return <Text style={styles.contentText}>Review and categories management</Text>;
      case 'Pricing':
        return <Text style={styles.contentText}>Pricing configuration</Text>;
      case 'Bookings Overview':
        return <Text style={styles.contentText}>Bookings overview and management</Text>;
      case 'Feedback & Reviews':
        return <Text style={styles.contentText}>User feedback and reviews</Text>;
      case 'System Settings':
        return <Text style={styles.contentText}>System configuration settings</Text>;
      default:
        return <Text style={styles.contentText}>Select a section from the sidebar</Text>;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoText}>Logo</Text>
        </View>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <View style={styles.profileContainer}>
          <Ionicons name="notifications-outline" size={24} color="#333" />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>Admin</Text>
            <Text style={styles.profileRole}>Logged in</Text>
          </View>
        </View>
      </View>

      <View style={styles.mainContent}>
        {/* Sidebar */}
        <View style={styles.sidebar}>
          {[
            'Dashboard',
            'User Management',
            'Events',
            'Review & Categories',
            'Pricing',
            'Bookings Overview',
            'Feedback & Reviews',
            'System Settings'
          ].map((item) => (
            <TouchableOpacity
              key={item}
              style={[
                styles.sidebarItem,
                activeSection === item && styles.activeSidebarItem
              ]}
              onPress={() => setActiveSection(item)}
            >
              <Text
                style={[
                  styles.sidebarText,
                  activeSection === item && styles.activeSidebarText
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content Area */}
        <View style={styles.content}>
          <Text style={styles.contentTitle}>{activeSection}</Text>
          <ScrollView style={styles.contentScroll}>
            {renderContent()}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  logoContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
  },
  logoText: {
    fontWeight: 'bold',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    marginHorizontal: 15,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 5,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileInfo: {
    marginLeft: 10,
  },
  profileName: {
    fontWeight: 'bold',
  },
  profileRole: {
    fontSize: 12,
    color: '#666',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 200,
    backgroundColor: '#fff',
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
    padding: 10,
  },
  sidebarItem: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 5,
  },
  activeSidebarItem: {
    backgroundColor: '#e6f7ff',
  },
  sidebarText: {
    fontWeight: '500',
  },
  activeSidebarText: {
    color: '#1890ff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  contentScroll: {
    flex: 1,
  },
  contentText: {
    fontSize: 16,
    color: '#666',
  },
});

export default AdminScreen;