const Vendor = require('../models/Vendor');

// Get all vendors
exports.getVendors = async (req, res) => {
  try {
    const vendors = await Vendor.findAll();
    
    // Remove password from response
    const vendorsWithoutPassword = vendors.map(vendor => {
      const { password, ...vendorWithoutPassword } = vendor;
      return vendorWithoutPassword;
    });
    
    res.status(200).json({
      success: true,
      count: vendorsWithoutPassword.length,
      data: vendorsWithoutPassword
    });
  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vendors',
      error: error.message
    });
  }
};

// Get single vendor
exports.getVendor = async (req, res) => {
  try {
    const vendor = await Vendor.findById(req.params.id);
    
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }
    
    // Remove password from response
    const { password, ...vendorWithoutPassword } = vendor;
    
    res.status(200).json({
      success: true,
      data: vendorWithoutPassword
    });
  } catch (error) {
    console.error('Get vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting vendor',
      error: error.message
    });
  }
};

// Create vendor
exports.createVendor = async (req, res) => {
  try {
    const { 
      name, 
      email, 
      phone, 
      businessName, 
      category, 
      status, 
      address, 
      description, 
      password 
    } = req.body;
    
    // Check if vendor already exists
    const existingVendor = await Vendor.findByEmail(email);
    const existingBusinessName = await Vendor.findByBusinessName(businessName);
    
    if (existingVendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor with this email already exists'
      });
    }
    
    if (existingBusinessName) {
      return res.status(400).json({
        success: false,
        message: 'Vendor with this business name already exists'
      });
    }
    
    // Create new vendor
    const vendor = new Vendor({
      name,
      email,
      phone,
      businessName,
      category,
      status,
      address,
      description,
      password,
      joinedOn: new Date().toISOString(),
      rating: 0,
      totalOrders: 0
    });
    
    // Save vendor to database
    await vendor.save();
    
    // Remove password from response
    const { password: pwd, ...vendorWithoutPassword } = vendor;
    
    res.status(201).json({
      success: true,
      message: 'Vendor created successfully',
      data: vendorWithoutPassword
    });
  } catch (error) {
    console.error('Create vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating vendor',
      error: error.message
    });
  }
};

// Update vendor
exports.updateVendor = async (req, res) => {
  try {
    const { 
      name, 
      email, 
      phone, 
      businessName, 
      category, 
      status, 
      address, 
      description, 
      password 
    } = req.body;
    
    // Check if vendor exists
    let vendor = await Vendor.findById(req.params.id);
    
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }
    
    // Check if email is already taken by another vendor
    if (email && email !== vendor.email) {
      const existingVendor = await Vendor.findByEmail(email);
      if (existingVendor && existingVendor._id !== vendor._id) {
        return res.status(400).json({
          success: false,
          message: 'Email is already taken by another vendor'
        });
      }
    }
    
    // Check if business name is already taken by another vendor
    if (businessName && businessName !== vendor.businessName) {
      const existingBusinessName = await Vendor.findByBusinessName(businessName);
      if (existingBusinessName && existingBusinessName._id !== vendor._id) {
        return res.status(400).json({
          success: false,
          message: 'Business name is already taken by another vendor'
        });
      }
    }
    
    // Update vendor fields
    vendor.name = name || vendor.name;
    vendor.email = email || vendor.email;
    vendor.phone = phone || vendor.phone;
    vendor.businessName = businessName || vendor.businessName;
    vendor.category = category || vendor.category;
    vendor.status = status || vendor.status;
    vendor.address = address || vendor.address;
    vendor.description = description || vendor.description;
    
    // Only update password if provided
    if (password) {
      vendor.password = password;
    }
    
    // Save updated vendor
    await vendor.update();
    
    // Remove password from response
    const { password: pwd, ...vendorWithoutPassword } = vendor;
    
    res.status(200).json({
      success: true,
      message: 'Vendor updated successfully',
      data: vendorWithoutPassword
    });
  } catch (error) {
    console.error('Update vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating vendor',
      error: error.message
    });
  }
};

// Delete vendor
exports.deleteVendor = async (req, res) => {
  try {
    // Check if vendor exists
    const vendor = await Vendor.findById(req.params.id);
    
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }
    
    // Delete vendor
    await Vendor.deleteById(req.params.id);
    
    res.status(200).json({
      success: true,
      message: 'Vendor deleted successfully'
    });
  } catch (error) {
    console.error('Delete vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting vendor',
      error: error.message
    });
  }
};
