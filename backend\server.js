const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors({
  origin: ['http://localhost:8081', 'http://**************:8081', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/eventease_admin';

mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => {
  console.log('✅ Connected to MongoDB');
})
.catch((error) => {
  console.error('❌ MongoDB connection error:', error);
  console.log('⚠️ Running with mock data only');
});

// Mock data for dashboard stats
const mockStats = {
  totalUsers: 150,
  activeVendors: 45,
  totalBookings: 230,
  totalEvents: 89
};

// Mock data for other endpoints
const mockUsers = [
  { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'user', status: 'active' },
  { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'user', status: 'inactive' }
];

const mockVendors = [
  { id: 1, name: 'Event Planners Pro', email: '<EMAIL>', status: 'approved', category: 'Event Planning' },
  { id: 2, name: 'Catering Masters', email: '<EMAIL>', status: 'pending', category: 'Catering' },
  { id: 3, name: 'Sound & Light Co', email: '<EMAIL>', status: 'approved', category: 'Audio/Visual' }
];

const mockBookings = [
  { id: 1, eventName: 'Wedding Ceremony', customerName: 'Alice Johnson', date: '2024-06-15', status: 'confirmed' },
  { id: 2, eventName: 'Corporate Meeting', customerName: 'Tech Corp', date: '2024-06-20', status: 'pending' },
  { id: 3, eventName: 'Birthday Party', customerName: 'Mike Brown', date: '2024-06-25', status: 'confirmed' }
];

const mockEvents = [
  { id: 1, name: 'Summer Music Festival', date: '2024-07-15', location: 'Central Park', status: 'active' },
  { id: 2, name: 'Tech Conference 2024', date: '2024-08-10', location: 'Convention Center', status: 'active' },
  { id: 3, name: 'Food & Wine Expo', date: '2024-09-05', location: 'Exhibition Hall', status: 'draft' }
];

// Import Routes
const authRoutes = require('./routes/authRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const userRoutes = require('./routes/userRoutes');
const vendorRoutes = require('./routes/vendorRoutes');
const pricingRoutes = require('./routes/pricingRoutes');
const bookingRoutes = require('./routes/bookingRoutes');
const eventRoutes = require('./routes/eventRoutes');
const settingsRoutes = require('./routes/settingsRoutes');
const securityRoutes = require('./routes/securityRoutes');

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/users', userRoutes);
app.use('/api/vendors', vendorRoutes);
app.use('/api/pricing', pricingRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/events', eventRoutes);
app.use('/api/settings', settingsRoutes);
app.use('/api/security', securityRoutes);



// Health check route
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'EventEase Admin Backend is running!',
    timestamp: new Date().toISOString()
  });
});

// Default route
app.get('/', (req, res) => {
  res.json({
    message: 'EventEase Admin Backend API',
    version: '2.0.0',
    description: 'Comprehensive Event Management System',
    endpoints: {
      health: '/api/health',
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        profile: 'GET /api/auth/profile',
        logout: 'POST /api/auth/logout',
        verify: 'GET /api/auth/verify'
      },
      dashboard: {
        stats: '/api/dashboard/stats',
        health: '/api/dashboard/health',
        activity: '/api/dashboard/activity'
      },
      users: {
        list: '/api/users',
        stats: '/api/users/stats',
        create: 'POST /api/users',
        update: 'PUT /api/users/:id',
        delete: 'DELETE /api/users/:id'
      },
      vendors: {
        list: '/api/vendors',
        stats: '/api/vendors/stats',
        approve: 'PUT /api/vendors/:id/approve',
        reject: 'PUT /api/vendors/:id/reject',
        suspend: 'PUT /api/vendors/:id/suspend'
      },
      pricing: '/api/pricing',
      bookings: '/api/bookings',
      events: '/api/events',
      settings: '/api/settings',
      security: '/api/security'
    },
    features: [
      'JWT Authentication & Authorization',
      'Admin Registration & Login System',
      'User Management with Role-based Access',
      'Vendor Application & Approval System',
      'Real-time Dashboard Analytics',
      'MongoDB Integration',
      'RESTful API Design',
      'Comprehensive Error Handling',
      'Account Security & Login Attempts Tracking'
    ]
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 EventEase Admin Backend running on port ${PORT}`);
  console.log(`📊 Dashboard API: http://localhost:${PORT}/api/dashboard/stats`);
  console.log(`🌐 Mobile API: http://**************:${PORT}/api/dashboard/stats`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📱 Mobile Health: http://**************:${PORT}/api/health`);
});

module.exports = app;
