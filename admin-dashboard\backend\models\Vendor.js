const bcrypt = require('bcryptjs');
const db = require('../mockDb');

// Vendor model for mock database
class Vendor {
  constructor(data) {
    this._id = data._id || Date.now().toString();
    this.name = data.name;
    this.email = data.email;
    this.phone = data.phone || '';
    this.businessName = data.businessName;
    this.category = data.category || 'restaurant';
    this.status = data.status || 'active';
    this.address = data.address || '';
    this.description = data.description || '';
    this.password = data.password || '';
    this.joinedOn = data.joinedOn || new Date().toISOString();
    this.rating = data.rating || 0;
    this.totalOrders = data.totalOrders || 0;
  }

  // Save vendor to database
  async save() {
    // Check if vendor already exists
    const existingVendor = db.vendors.find(
      vendor => vendor.email === this.email || vendor.businessName === this.businessName
    );
    
    if (existingVendor) {
      throw new Error('Vendor already exists');
    }
    
    // Hash password if provided and not already hashed
    if (this.password && !this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Add to database
    db.vendors.push(this);
    return this;
  }

  // Update vendor
  async update() {
    const index = db.vendors.findIndex(vendor => vendor._id === this._id);
    
    if (index === -1) {
      throw new Error('Vendor not found');
    }
    
    // Hash password if changed
    if (this.password && !this.password.startsWith('$2a$')) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
    }
    
    // Update in database
    db.vendors[index] = this;
    return this;
  }

  // Find vendor by ID
  static async findById(id) {
    const vendor = db.vendors.find(vendor => vendor._id === id);
    
    if (!vendor) {
      return null;
    }
    
    return new Vendor(vendor);
  }

  // Find vendor by email
  static async findByEmail(email) {
    const vendor = db.vendors.find(vendor => vendor.email === email);
    
    if (!vendor) {
      return null;
    }
    
    return new Vendor(vendor);
  }

  // Find vendor by business name
  static async findByBusinessName(businessName) {
    const vendor = db.vendors.find(vendor => vendor.businessName === businessName);
    
    if (!vendor) {
      return null;
    }
    
    return new Vendor(vendor);
  }

  // Find all vendors
  static async findAll() {
    return db.vendors.map(vendor => new Vendor(vendor));
  }

  // Delete vendor
  static async deleteById(id) {
    const index = db.vendors.findIndex(vendor => vendor._id === id);
    
    if (index === -1) {
      throw new Error('Vendor not found');
    }
    
    // Remove from database
    db.vendors.splice(index, 1);
    return true;
  }

  // Select fields to return
  select(fields) {
    if (fields === '-password') {
      const { password, ...vendorWithoutPassword } = this;
      return vendorWithoutPassword;
    }
    return this;
  }
}

module.exports = Vendor;
