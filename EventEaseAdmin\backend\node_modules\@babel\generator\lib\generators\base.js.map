{"version": 3, "names": ["File", "node", "program", "print", "interpreter", "Program", "_node$directives", "noIndentInnerCommentsHere", "printInnerComments", "directivesLen", "directives", "length", "_node$directives$trai", "newline", "body", "printSequence", "undefined", "trailingComments", "BlockStatement", "_node$directives2", "token", "exit", "enterDelimited", "_node$directives$trai2", "rightBrace", "Directive", "value", "semicolon", "unescapedSingleQuoteRE", "unescapedDoubleQuoteRE", "DirectiveLiteral", "raw", "getPossibleRaw", "format", "minified", "test", "Error", "InterpreterDirective", "Placeholder", "name", "expectedNode"], "sources": ["../../src/generators/base.ts"], "sourcesContent": ["import type Printer from \"../printer.ts\";\nimport type * as t from \"@babel/types\";\n\nexport function File(this: Printer, node: t.File) {\n  if (node.program) {\n    // Print this here to ensure that Program node 'leadingComments' still\n    // get printed after the hashbang.\n    this.print(node.program.interpreter);\n  }\n\n  this.print(node.program);\n}\n\nexport function Program(this: Printer, node: t.Program) {\n  // An empty Program doesn't have any inner tokens, so\n  // we must explicitly print its inner comments.\n  this.noIndentInnerCommentsHere();\n  this.printInnerComments();\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, undefined, newline);\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body);\n}\n\nexport function BlockStatement(this: Printer, node: t.BlockStatement) {\n  this.token(\"{\");\n  const exit = this.enterDelimited();\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, true, newline);\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body, true);\n\n  exit();\n  this.rightBrace(node);\n}\n\nexport function Directive(this: Printer, node: t.Directive) {\n  this.print(node.value);\n  this.semicolon();\n}\n\n// These regexes match an even number of \\ followed by a quote\nconst unescapedSingleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*'/;\nconst unescapedDoubleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*\"/;\n\nexport function DirectiveLiteral(this: Printer, node: t.DirectiveLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const { value } = node;\n\n  // NOTE: In directives we can't change escapings,\n  // because they change the behavior.\n  // e.g. \"us\\x65 strict\" (\\x65 is e) is not a \"use strict\" directive.\n\n  if (!unescapedDoubleQuoteRE.test(value)) {\n    this.token(`\"${value}\"`);\n  } else if (!unescapedSingleQuoteRE.test(value)) {\n    this.token(`'${value}'`);\n  } else {\n    throw new Error(\n      \"Malformed AST: it is not possible to print a directive containing\" +\n        \" both unescaped single and double quotes.\",\n    );\n  }\n}\n\nexport function InterpreterDirective(\n  this: Printer,\n  node: t.InterpreterDirective,\n) {\n  this.token(`#!${node.value}`);\n  this.newline(1, true);\n}\n\nexport function Placeholder(this: Printer, node: t.Placeholder) {\n  this.token(\"%%\");\n  this.print(node.name);\n  this.token(\"%%\");\n\n  if (node.expectedNode === \"Statement\") {\n    this.semicolon();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;AAGO,SAASA,IAAIA,CAAgBC,IAAY,EAAE;EAChD,IAAIA,IAAI,CAACC,OAAO,EAAE;IAGhB,IAAI,CAACC,KAAK,CAACF,IAAI,CAACC,OAAO,CAACE,WAAW,CAAC;EACtC;EAEA,IAAI,CAACD,KAAK,CAACF,IAAI,CAACC,OAAO,CAAC;AAC1B;AAEO,SAASG,OAAOA,CAAgBJ,IAAe,EAAE;EAAA,IAAAK,gBAAA;EAGtD,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAChC,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAEzB,MAAMC,aAAa,IAAAH,gBAAA,GAAGL,IAAI,CAACS,UAAU,qBAAfJ,gBAAA,CAAiBK,MAAM;EAC7C,IAAIF,aAAa,EAAE;IAAA,IAAAG,qBAAA;IACjB,MAAMC,OAAO,GAAGZ,IAAI,CAACa,IAAI,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;IACxC,IAAI,CAACI,aAAa,CAACd,IAAI,CAACS,UAAU,EAAEM,SAAS,EAAEH,OAAO,CAAC;IACvD,IAAI,GAAAD,qBAAA,GAACX,IAAI,CAACS,UAAU,CAACD,aAAa,GAAG,CAAC,CAAC,CAACQ,gBAAgB,aAAnDL,qBAAA,CAAqDD,MAAM,GAAE;MAChE,IAAI,CAACE,OAAO,CAACA,OAAO,CAAC;IACvB;EACF;EAEA,IAAI,CAACE,aAAa,CAACd,IAAI,CAACa,IAAI,CAAC;AAC/B;AAEO,SAASI,cAAcA,CAAgBjB,IAAsB,EAAE;EAAA,IAAAkB,iBAAA;EACpE,IAAI,CAACC,SAAK,IAAI,CAAC;EACf,MAAMC,IAAI,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EAElC,MAAMb,aAAa,IAAAU,iBAAA,GAAGlB,IAAI,CAACS,UAAU,qBAAfS,iBAAA,CAAiBR,MAAM;EAC7C,IAAIF,aAAa,EAAE;IAAA,IAAAc,sBAAA;IACjB,MAAMV,OAAO,GAAGZ,IAAI,CAACa,IAAI,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC;IACxC,IAAI,CAACI,aAAa,CAACd,IAAI,CAACS,UAAU,EAAE,IAAI,EAAEG,OAAO,CAAC;IAClD,IAAI,GAAAU,sBAAA,GAACtB,IAAI,CAACS,UAAU,CAACD,aAAa,GAAG,CAAC,CAAC,CAACQ,gBAAgB,aAAnDM,sBAAA,CAAqDZ,MAAM,GAAE;MAChE,IAAI,CAACE,OAAO,CAACA,OAAO,CAAC;IACvB;EACF;EAEA,IAAI,CAACE,aAAa,CAACd,IAAI,CAACa,IAAI,EAAE,IAAI,CAAC;EAEnCO,IAAI,CAAC,CAAC;EACN,IAAI,CAACG,UAAU,CAACvB,IAAI,CAAC;AACvB;AAEO,SAASwB,SAASA,CAAgBxB,IAAiB,EAAE;EAC1D,IAAI,CAACE,KAAK,CAACF,IAAI,CAACyB,KAAK,CAAC;EACtB,IAAI,CAACC,SAAS,CAAC,CAAC;AAClB;AAGA,MAAMC,sBAAsB,GAAG,uBAAuB;AACtD,MAAMC,sBAAsB,GAAG,uBAAuB;AAE/C,SAASC,gBAAgBA,CAAgB7B,IAAwB,EAAE;EACxE,MAAM8B,GAAG,GAAG,IAAI,CAACC,cAAc,CAAC/B,IAAI,CAAC;EACrC,IAAI,CAAC,IAAI,CAACgC,MAAM,CAACC,QAAQ,IAAIH,GAAG,KAAKf,SAAS,EAAE;IAC9C,IAAI,CAACI,KAAK,CAACW,GAAG,CAAC;IACf;EACF;EAEA,MAAM;IAAEL;EAAM,CAAC,GAAGzB,IAAI;EAMtB,IAAI,CAAC4B,sBAAsB,CAACM,IAAI,CAACT,KAAK,CAAC,EAAE;IACvC,IAAI,CAACN,KAAK,CAAC,IAAIM,KAAK,GAAG,CAAC;EAC1B,CAAC,MAAM,IAAI,CAACE,sBAAsB,CAACO,IAAI,CAACT,KAAK,CAAC,EAAE;IAC9C,IAAI,CAACN,KAAK,CAAC,IAAIM,KAAK,GAAG,CAAC;EAC1B,CAAC,MAAM;IACL,MAAM,IAAIU,KAAK,CACb,mEAAmE,GACjE,2CACJ,CAAC;EACH;AACF;AAEO,SAASC,oBAAoBA,CAElCpC,IAA4B,EAC5B;EACA,IAAI,CAACmB,KAAK,CAAC,KAAKnB,IAAI,CAACyB,KAAK,EAAE,CAAC;EAC7B,IAAI,CAACb,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC;AACvB;AAEO,SAASyB,WAAWA,CAAgBrC,IAAmB,EAAE;EAC9D,IAAI,CAACmB,KAAK,CAAC,IAAI,CAAC;EAChB,IAAI,CAACjB,KAAK,CAACF,IAAI,CAACsC,IAAI,CAAC;EACrB,IAAI,CAACnB,KAAK,CAAC,IAAI,CAAC;EAEhB,IAAInB,IAAI,CAACuC,YAAY,KAAK,WAAW,EAAE;IACrC,IAAI,CAACb,SAAS,CAAC,CAAC;EAClB;AACF", "ignoreList": []}