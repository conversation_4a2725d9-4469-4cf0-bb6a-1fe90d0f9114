import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, TouchableOpacity, ScrollView, Image, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import LogoutButton from './LogoutButton';
import UserManagementScreen from './UserManagementScreen';
import VendorManagementScreen from './VendorManagementScreen';

const AdminScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSection, setActiveSection] = useState('Dashboard');
  const [isProfileDropdownVisible, setProfileDropdownVisible] = useState(false);
  const { logout } = useAuth();

  const renderDashboard = () => {
    return (
      <View style={styles.dashboardContainer}>
        <Text style={styles.dashboardTitle}>Control Panel</Text>
        <View style={styles.controlPanelRow}>
          <View style={[styles.controlPanelCard, styles.totalBookingsCard]}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Total Bookings</Text>
              <Ionicons name="calendar-outline" size={24} color="#4CAF50" />
            </View>
            <Text style={styles.cardValue}>1,248</Text>
            <Text style={styles.cardTrend}>+12% from last month</Text>
          </View>
          <View style={[styles.controlPanelCard, styles.activeUsersCard]}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Active Users</Text>
              <Ionicons name="people-outline" size={24} color="#2196F3" />
            </View>
            <Text style={styles.cardValue}>847</Text>
            <Text style={styles.cardTrend}>+5% from last month</Text>
          </View>
          <View style={[styles.controlPanelCard, styles.revenueCard]}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Revenue/Brands</Text>
              <Ionicons name="cash-outline" size={24} color="#FF9800" />
            </View>
            <Text style={styles.cardValue}>$24,500</Text>
            <Text style={styles.cardTrend}>+8% from last month</Text>
          </View>
          <View style={[styles.controlPanelCard, styles.pendingApprovalCard]}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Pending Approval</Text>
              <Ionicons name="time-outline" size={24} color="#F44336" />
            </View>
            <Text style={styles.cardValue}>23</Text>
            <Text style={[styles.cardTrend, styles.negativeTrend]}>-15% from last month</Text>
          </View>
        </View>
        <View style={styles.controlPanelRow}>
          <View style={[styles.controlPanelCard, styles.topLocationCard]}>
            <View style={styles.cardHeader}>
              <Text style={styles.cardTitle}>Top Location & Timing</Text>
              <Ionicons name="location-outline" size={24} color="#9C27B0" />
            </View>
            <View style={styles.locationList}>
              <View style={styles.locationItem}>
                <View style={styles.locationNameContainer}>
                  <Ionicons name="location" size={16} color="#9C27B0" style={styles.locationIcon} />
                  <Text style={styles.locationName}>New York</Text>
                </View>
                <Text style={styles.locationValue}>245 bookings</Text>
              </View>
              <View style={styles.locationItem}>
                <View style={styles.locationNameContainer}>
                  <Ionicons name="location" size={16} color="#9C27B0" style={styles.locationIcon} />
                  <Text style={styles.locationName}>Los Angeles</Text>
                </View>
                <Text style={styles.locationValue}>189 bookings</Text>
              </View>
              <View style={styles.locationItem}>
                <View style={styles.locationNameContainer}>
                  <Ionicons name="location" size={16} color="#9C27B0" style={styles.locationIcon} />
                  <Text style={styles.locationName}>Chicago</Text>
                </View>
                <Text style={styles.locationValue}>156 bookings</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    );
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'Dashboard':
        return renderDashboard();
      case 'User Management':
        return <UserManagementScreen />;
      case 'Vendor Management':
        return <VendorManagementScreen />;
      case 'Events':
        return <Text style={styles.contentText}>Events management</Text>;
      case 'Review & Categories':
        return <Text style={styles.contentText}>Review and categories management</Text>;
      case 'Pricing':
        return <Text style={styles.contentText}>Pricing configuration</Text>;
      case 'Bookings Overview':
        return <Text style={styles.contentText}>Bookings overview and management</Text>;
      case 'Feedback & Reviews':
        return <Text style={styles.contentText}>User feedback and reviews</Text>;
      case 'System Settings':
        return <Text style={styles.contentText}>System configuration settings</Text>;
      default:
        return <Text style={styles.contentText}>Select a section from the sidebar</Text>;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.logoContainer}>
          <Image source={require('../assets/icon.png')} style={styles.logoImage} />
        </View>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        <View style={styles.profileContainer}>
          <Ionicons name="notifications-outline" size={24} color="#333" style={styles.notificationIcon} />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>Admin</Text>
            <Text style={styles.profileRole}>Logged in</Text>
          </View>
          <LogoutButton />
        </View>
      </View>

      <View style={styles.mainContent}>
        {/* Sidebar */}
        <View style={styles.sidebar}>
          {[
            'Dashboard',
            'User Management',
            'Vendor Management',
            'Events',
            'Review & Categories',
            'Pricing',
            'Bookings Overview',
            'Feedback & Reviews',
            'System Settings'
          ].map((item) => (
            <TouchableOpacity
              key={item}
              style={[
                styles.sidebarItem,
                activeSection === item && styles.activeSidebarItem
              ]}
              onPress={() => setActiveSection(item)}
            >
              <Text
                style={[
                  styles.sidebarText,
                  activeSection === item && styles.activeSidebarText
                ]}
              >
                {item}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Content Area */}
        <View style={styles.content}>
          <Text style={styles.contentTitle}>{activeSection}</Text>
          <ScrollView style={styles.contentScroll}>
            {renderContent()}
          </ScrollView>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  dashboardContainer: {
    padding: 20,
  },
  dashboardTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333',
  },
  controlPanelRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  controlPanelCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    flex: 1,
    minWidth: 200,
    marginRight: 15,
  },
  totalBookingsCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  activeUsersCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  revenueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  pendingApprovalCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  topLocationCard: {
    flex: 1,
    borderLeftWidth: 4,
    borderLeftColor: '#9C27B0',
    maxWidth: '100%',
  },
  cardTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
    fontWeight: '500',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTrend: {
    fontSize: 14,
    color: '#4CAF50',
  },
  negativeTrend: {
    color: '#F44336',
  },
  locationList: {
    marginTop: 15,
  },
  locationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  locationNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    marginRight: 8,
  },
  locationName: {
    fontSize: 16,
    color: '#333',
  },
  locationValue: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  logoContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  logoImage: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    marginHorizontal: 15,
    paddingHorizontal: 10,
  },
  searchIcon: {
    marginRight: 5,
  },
  searchInput: {
    flex: 1,
    height: 40,
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  notificationIcon: {
    marginRight: 15,
  },
  profileInfo: {
    marginLeft: 5,
    paddingHorizontal: 5,
    paddingVertical: 3,
    borderRadius: 4,
  },
  profileRoleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileName: {
    fontWeight: 'bold',
  },
  profileRole: {
    fontSize: 12,
    color: '#666',
    marginRight: 5,
  },
  profileDropdown: {
    position: 'absolute',
    top: 50,
    right: 0,
    width: 200,
    backgroundColor: 'white',
    borderRadius: 5,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    zIndex: 1000,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownIcon: {
    marginRight: 10,
  },
  dropdownText: {
    fontSize: 14,
  },
  mainContent: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 200,
    backgroundColor: '#fff',
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
    padding: 10,
  },
  sidebarItem: {
    padding: 15,
    borderRadius: 8,
    marginBottom: 5,
  },
  activeSidebarItem: {
    backgroundColor: '#e6f7ff',
  },
  sidebarText: {
    fontWeight: '500',
  },
  activeSidebarText: {
    color: '#1890ff',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  contentScroll: {
    flex: 1,
  },
  contentText: {
    fontSize: 16,
    color: '#666',
  },
});

export default AdminScreen;
