const express = require('express');
const router = express.Router();

// Mock pricing data
const mockPricing = [
  { id: 1, name: 'Basic Plan', price: 99, duration: 'monthly', features: ['Basic Support', '10 Events'] },
  { id: 2, name: 'Pro Plan', price: 199, duration: 'monthly', features: ['Priority Support', '50 Events', 'Analytics'] },
  { id: 3, name: 'Enterprise Plan', price: 499, duration: 'monthly', features: ['24/7 Support', 'Unlimited Events', 'Advanced Analytics'] }
];

// GET /api/pricing - Get all pricing plans
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockPricing,
      total: mockPricing.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pricing plans',
      error: error.message
    });
  }
});

// POST /api/pricing - Create new pricing plan
router.post('/', (req, res) => {
  try {
    const { name, price, duration, features } = req.body;
    
    if (!name || !price) {
      return res.status(400).json({
        success: false,
        message: 'Name and price are required'
      });
    }
    
    const newPlan = {
      id: mockPricing.length + 1,
      name,
      price,
      duration: duration || 'monthly',
      features: features || []
    };
    
    mockPricing.push(newPlan);
    
    res.status(201).json({
      success: true,
      data: newPlan,
      message: 'Pricing plan created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to create pricing plan',
      error: error.message
    });
  }
});

module.exports = router;
