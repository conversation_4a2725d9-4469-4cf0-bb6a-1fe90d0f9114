const express = require('express');
const router = express.Router();

// Mock settings data
const mockSettings = {
  siteName: 'EventEase Admin',
  siteDescription: 'Professional Event Management Platform',
  contactEmail: '<EMAIL>',
  timezone: 'UTC',
  currency: 'USD',
  maintenanceMode: false
};

// GET /api/settings - Get all settings
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockSettings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch settings',
      error: error.message
    });
  }
});

// PUT /api/settings - Update settings
router.put('/', (req, res) => {
  try {
    const updates = req.body;
    
    Object.keys(updates).forEach(key => {
      if (mockSettings.hasOwnProperty(key)) {
        mockSettings[key] = updates[key];
      }
    });
    
    res.json({
      success: true,
      data: mockSettings,
      message: 'Settings updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
});

module.exports = router;
