{"name": "eventease-admin-backend", "version": "1.0.0", "description": "Professional EventEase Admin Dashboard Backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "moment": "^2.29.4", "uuid": "^9.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "express-mongo-sanitize": "^2.2.0", "xss-clean": "^0.1.4", "hpp": "^0.2.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "keywords": ["eventease", "admin", "dashboard", "backend", "api"], "author": "EventEase Team", "license": "MIT"}