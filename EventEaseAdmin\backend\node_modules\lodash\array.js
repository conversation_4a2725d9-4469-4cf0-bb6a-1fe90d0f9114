module.exports = {
  'chunk': require('./chunk'),
  'compact': require('./compact'),
  'concat': require('./concat'),
  'difference': require('./difference'),
  'differenceBy': require('./differenceBy'),
  'differenceWith': require('./differenceWith'),
  'drop': require('./drop'),
  'dropRight': require('./dropRight'),
  'dropRightWhile': require('./dropRightWhile'),
  'dropWhile': require('./dropWhile'),
  'fill': require('./fill'),
  'findIndex': require('./findIndex'),
  'findLastIndex': require('./findLastIndex'),
  'first': require('./first'),
  'flatten': require('./flatten'),
  'flattenDeep': require('./flattenDeep'),
  'flattenDepth': require('./flattenDepth'),
  'fromPairs': require('./fromPairs'),
  'head': require('./head'),
  'indexOf': require('./indexOf'),
  'initial': require('./initial'),
  'intersection': require('./intersection'),
  'intersectionBy': require('./intersectionBy'),
  'intersectionWith': require('./intersectionWith'),
  'join': require('./join'),
  'last': require('./last'),
  'lastIndexOf': require('./lastIndexOf'),
  'nth': require('./nth'),
  'pull': require('./pull'),
  'pullAll': require('./pullAll'),
  'pullAllBy': require('./pullAllBy'),
  'pullAllWith': require('./pullAllWith'),
  'pullAt': require('./pullAt'),
  'remove': require('./remove'),
  'reverse': require('./reverse'),
  'slice': require('./slice'),
  'sortedIndex': require('./sortedIndex'),
  'sortedIndexBy': require('./sortedIndexBy'),
  'sortedIndexOf': require('./sortedIndexOf'),
  'sortedLastIndex': require('./sortedLastIndex'),
  'sortedLastIndexBy': require('./sortedLastIndexBy'),
  'sortedLastIndexOf': require('./sortedLastIndexOf'),
  'sortedUniq': require('./sortedUniq'),
  'sortedUniqBy': require('./sortedUniqBy'),
  'tail': require('./tail'),
  'take': require('./take'),
  'takeRight': require('./takeRight'),
  'takeRightWhile': require('./takeRightWhile'),
  'takeWhile': require('./takeWhile'),
  'union': require('./union'),
  'unionBy': require('./unionBy'),
  'unionWith': require('./unionWith'),
  'uniq': require('./uniq'),
  'uniqBy': require('./uniqBy'),
  'uniqWith': require('./uniqWith'),
  'unzip': require('./unzip'),
  'unzipWith': require('./unzipWith'),
  'without': require('./without'),
  'xor': require('./xor'),
  'xorBy': require('./xorBy'),
  'xorWith': require('./xorWith'),
  'zip': require('./zip'),
  'zipObject': require('./zipObject'),
  'zipObjectDeep': require('./zipObjectDeep'),
  'zipWith': require('./zipWith')
};
