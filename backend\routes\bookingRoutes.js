const express = require('express');
const router = express.Router();

// Mock booking data
const mockBookings = [
  { id: 1, eventName: 'Wedding Ceremony', customerName: '<PERSON>', date: '2024-06-15', status: 'confirmed' },
  { id: 2, eventName: 'Corporate Meeting', customerName: 'Tech Corp', date: '2024-06-20', status: 'pending' },
  { id: 3, eventName: 'Birthday Party', customerName: '<PERSON>', date: '2024-06-25', status: 'confirmed' }
];

// GET /api/bookings - Get all bookings
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockBookings,
      total: mockBookings.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to fetch bookings',
      error: error.message
    });
  }
});

module.exports = router;
