"use strict";

function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return typeof key === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (typeof input !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (typeof res !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
const Stream = require('stream');
const net = require('net');
const tls = require('tls');
// eslint-disable-next-line node/no-deprecated-api
const _require = require('url'),
  parse = _require.parse;
const process = require('process');
const semverGte = require('semver/functions/gte');
let http2;
if (semverGte(process.version, 'v10.10.0')) http2 = require('http2');else throw new Error('superagent: this version of Node.js does not support http2');
const _http2$constants = http2.constants,
  HTTP2_HEADER_PATH = _http2$constants.HTTP2_HEADER_PATH,
  HTTP2_HEADER_STATUS = _http2$constants.HTTP2_HEADER_STATUS,
  HTTP2_HEADER_METHOD = _http2$constants.HTTP2_HEADER_METHOD,
  HTTP2_HEADER_AUTHORITY = _http2$constants.HTTP2_HEADER_AUTHORITY,
  HTTP2_HEADER_HOST = _http2$constants.HTTP2_HEADER_HOST,
  HTTP2_HEADER_SET_COOKIE = _http2$constants.HTTP2_HEADER_SET_COOKIE,
  NGHTTP2_CANCEL = _http2$constants.NGHTTP2_CANCEL;
function setProtocol(protocol) {
  return {
    request(options) {
      return new Request(protocol, options);
    }
  };
}
class Request extends Stream {
  constructor(protocol, options) {
    super();
    const defaultPort = protocol === 'https:' ? 443 : 80;
    const defaultHost = 'localhost';
    const port = options.port || defaultPort;
    const host = options.host || defaultHost;
    delete options.port;
    delete options.host;
    this.method = options.method;
    this.path = options.path;
    this.protocol = protocol;
    this.host = host;
    delete options.method;
    delete options.path;
    const sessionOptions = _objectSpread({}, options);
    if (options.socketPath) {
      sessionOptions.socketPath = options.socketPath;
      sessionOptions.createConnection = this.createUnixConnection.bind(this);
    }
    this._headers = {};
    const session = http2.connect(`${protocol}//${host}:${port}`, sessionOptions);
    this.setHeader('host', `${host}:${port}`);
    session.on('error', error => this.emit('error', error));
    this.session = session;
  }
  createUnixConnection(authority, options) {
    switch (this.protocol) {
      case 'http:':
        return net.connect(options.socketPath);
      case 'https:':
        options.ALPNProtocols = ['h2'];
        options.servername = this.host;
        options.allowHalfOpen = true;
        return tls.connect(options.socketPath, options);
      default:
        throw new Error('Unsupported protocol', this.protocol);
    }
  }
  setNoDelay(bool) {
    // We can not use setNoDelay with HTTP/2.
    // Node 10 limits http2session.socket methods to ones safe to use with HTTP/2.
    // See also https://nodejs.org/api/http2.html#http2_http2session_socket
  }
  getFrame() {
    if (this.frame) {
      return this.frame;
    }
    const method = {
      [HTTP2_HEADER_PATH]: this.path,
      [HTTP2_HEADER_METHOD]: this.method
    };
    let headers = this.mapToHttp2Header(this._headers);
    headers = Object.assign(headers, method);
    const frame = this.session.request(headers);
    frame.once('response', (headers, flags) => {
      headers = this.mapToHttpHeader(headers);
      frame.headers = headers;
      frame.statusCode = headers[HTTP2_HEADER_STATUS];
      frame.status = frame.statusCode;
      this.emit('response', frame);
    });
    this._headerSent = true;
    frame.once('drain', () => this.emit('drain'));
    frame.on('error', error => this.emit('error', error));
    frame.on('close', () => this.session.close());
    this.frame = frame;
    return frame;
  }
  mapToHttpHeader(headers) {
    const keys = Object.keys(headers);
    const http2Headers = {};
    for (var _i = 0, _keys = keys; _i < _keys.length; _i++) {
      let key = _keys[_i];
      let value = headers[key];
      key = key.toLowerCase();
      switch (key) {
        case HTTP2_HEADER_SET_COOKIE:
          value = Array.isArray(value) ? value : [value];
          break;
        default:
          break;
      }
      http2Headers[key] = value;
    }
    return http2Headers;
  }
  mapToHttp2Header(headers) {
    const keys = Object.keys(headers);
    const http2Headers = {};
    for (var _i2 = 0, _keys2 = keys; _i2 < _keys2.length; _i2++) {
      let key = _keys2[_i2];
      let value = headers[key];
      key = key.toLowerCase();
      switch (key) {
        case HTTP2_HEADER_HOST:
          key = HTTP2_HEADER_AUTHORITY;
          value = /^http:\/\/|^https:\/\//.test(value) ? parse(value).host : value;
          break;
        default:
          break;
      }
      http2Headers[key] = value;
    }
    return http2Headers;
  }
  setHeader(name, value) {
    this._headers[name.toLowerCase()] = value;
  }
  getHeader(name) {
    return this._headers[name.toLowerCase()];
  }
  write(data, encoding) {
    const frame = this.getFrame();
    return frame.write(data, encoding);
  }
  pipe(stream, options) {
    const frame = this.getFrame();
    return frame.pipe(stream, options);
  }
  end(data) {
    const frame = this.getFrame();
    frame.end(data);
  }
  abort(data) {
    const frame = this.getFrame();
    frame.close(NGHTTP2_CANCEL);
    this.session.destroy();
  }
}
exports.setProtocol = setProtocol;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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