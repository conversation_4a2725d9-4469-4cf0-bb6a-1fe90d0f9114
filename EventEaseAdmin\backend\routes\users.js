const express = require('express');
const { body, validationResult, query } = require('express-validator');
const User = require('../models/User');
const { requireAdmin, requireSuperAdmin } = require('../middleware/auth');

const router = express.Router();

// Helper function to handle validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      code: 'VALIDATION_ERROR',
      errors: errors.array().map(error => ({
        field: error.path,
        message: error.msg,
        value: error.value
      }))
    });
  }
  next();
};

// @route   GET /api/users
// @desc    Get all users with pagination and filtering
// @access  Private (Admin)
router.get('/', 
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('role').optional().isIn(['admin', 'super_admin', 'moderator', 'user']).withMessage('Invalid role'),
    query('status').optional().isIn(['active', 'inactive', 'suspended', 'pending']).withMessage('Invalid status'),
    query('search').optional().isLength({ min: 1 }).withMessage('Search term cannot be empty')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { 
        page = 1, 
        limit = 10, 
        role, 
        status, 
        search, 
        sortBy = 'createdAt', 
        sortOrder = 'desc' 
      } = req.query;

      // Build filter object
      const filter = {};
      if (role) filter.role = role;
      if (status) filter.status = status;
      if (search) {
        filter.$or = [
          { firstName: { $regex: search, $options: 'i' } },
          { lastName: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      // Build sort object
      const sort = {};
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get users with pagination
      const users = await User.find(filter)
        .select('-password')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('metadata.createdBy', 'firstName lastName email')
        .populate('metadata.updatedBy', 'firstName lastName email');

      // Get total count for pagination
      const total = await User.countDocuments(filter);
      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        message: 'Users retrieved successfully',
        data: {
          users,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   GET /api/users/stats
// @desc    Get user statistics
// @access  Private (Admin)
router.get('/stats', requireAdmin, async (req, res, next) => {
  try {
    const stats = await User.getStats();
    
    // Get additional statistics
    const roleDistribution = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    const statusDistribution = await User.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get recent registrations (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentRegistrations = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    res.json({
      success: true,
      message: 'User statistics retrieved successfully',
      data: {
        overview: stats,
        distribution: {
          byRole: roleDistribution.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {}),
          byStatus: statusDistribution.reduce((acc, item) => {
            acc[item._id] = item.count;
            return acc;
          }, {})
        },
        recentActivity: {
          registrations: recentRegistrations.map(item => ({
            date: new Date(item._id.year, item._id.month - 1, item._id.day),
            count: item.count
          }))
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Private (Admin)
router.get('/:id', requireAdmin, async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id)
      .select('-password')
      .populate('metadata.createdBy', 'firstName lastName email')
      .populate('metadata.updatedBy', 'firstName lastName email');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: 'User retrieved successfully',
      data: {
        user
      }
    });

  } catch (error) {
    next(error);
  }
});

// @route   PUT /api/users/:id
// @desc    Update user
// @access  Private (Admin)
router.put('/:id',
  requireAdmin,
  [
    body('firstName').optional().trim().isLength({ min: 2, max: 50 }).withMessage('First name must be between 2 and 50 characters'),
    body('lastName').optional().trim().isLength({ min: 2, max: 50 }).withMessage('Last name must be between 2 and 50 characters'),
    body('email').optional().isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
    body('phone').optional().isMobilePhone().withMessage('Please provide a valid phone number'),
    body('role').optional().isIn(['admin', 'super_admin', 'moderator', 'user']).withMessage('Invalid role'),
    body('status').optional().isIn(['active', 'inactive', 'suspended', 'pending']).withMessage('Invalid status')
  ],
  handleValidationErrors,
  async (req, res, next) => {
    try {
      const { firstName, lastName, email, phone, role, status, preferences } = req.body;

      // Check if user exists
      const existingUser = await User.findById(req.params.id);
      if (!existingUser) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
          code: 'USER_NOT_FOUND'
        });
      }

      // Check if email is already taken by another user
      if (email && email !== existingUser.email) {
        const emailExists = await User.findOne({ email, _id: { $ne: req.params.id } });
        if (emailExists) {
          return res.status(400).json({
            success: false,
            message: 'Email is already taken',
            code: 'EMAIL_EXISTS'
          });
        }
      }

      // Only super admin can change roles to super_admin
      if (role === 'super_admin' && req.userRole !== 'super_admin') {
        return res.status(403).json({
          success: false,
          message: 'Only super admin can assign super admin role',
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      // Prevent users from changing their own role (except super admin)
      if (req.params.id === req.userId.toString() && role && req.userRole !== 'super_admin') {
        return res.status(400).json({
          success: false,
          message: 'Cannot change your own role',
          code: 'CANNOT_CHANGE_OWN_ROLE'
        });
      }

      // Build update object
      const updateData = {};
      if (firstName) updateData.firstName = firstName;
      if (lastName) updateData.lastName = lastName;
      if (email) updateData.email = email;
      if (phone) updateData.phone = phone;
      if (role) updateData.role = role;
      if (status) updateData.status = status;
      if (preferences) updateData.preferences = { ...existingUser.preferences, ...preferences };
      
      updateData['metadata.updatedBy'] = req.userId;

      const user = await User.findByIdAndUpdate(
        req.params.id,
        updateData,
        { new: true, runValidators: true }
      ).select('-password');

      res.json({
        success: true,
        message: 'User updated successfully',
        data: {
          user
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

// @route   DELETE /api/users/:id
// @desc    Delete user
// @access  Private (Super Admin)
router.delete('/:id', requireSuperAdmin, async (req, res, next) => {
  try {
    // Check if user exists
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Prevent deletion of own account
    if (req.params.id === req.userId.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own account',
        code: 'CANNOT_DELETE_SELF'
      });
    }

    // Prevent deletion of other super admins (unless you're a super admin)
    if (user.role === 'super_admin' && req.userRole !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Cannot delete super admin account',
        code: 'CANNOT_DELETE_SUPER_ADMIN'
      });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    next(error);
  }
});

// @route   POST /api/users/:id/reset-password
// @desc    Reset user password (admin action)
// @access  Private (Super Admin)
router.post('/:id/reset-password', requireSuperAdmin, async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Generate temporary password
    const tempPassword = Math.random().toString(36).slice(-12) + 'A1!';
    
    user.password = tempPassword;
    user.metadata.updatedBy = req.userId;
    await user.save();

    res.json({
      success: true,
      message: 'Password reset successfully',
      data: {
        temporaryPassword: tempPassword,
        message: 'Please provide this temporary password to the user and ask them to change it immediately'
      }
    });

  } catch (error) {
    next(error);
  }
});

module.exports = router;
