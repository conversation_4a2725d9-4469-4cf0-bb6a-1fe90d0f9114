{"name": "istanbul-lib-source-maps", "version": "4.0.1", "description": "Source maps support for istanbul", "author": "<PERSON><PERSON> <kananthm<PERSON>-<EMAIL>>", "main": "index.js", "files": ["lib", "index.js"], "scripts": {"test": "nyc mocha"}, "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "devDependencies": {"chai": "^4.2.0", "mocha": "^6.2.2", "nyc": "^15.0.0-beta.2", "ts-node": "^8.5.4"}, "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/istanbuljs/istanbuljs/issues"}, "homepage": "https://istanbul.js.org/", "repository": {"type": "git", "url": "git+ssh://**************/istanbuljs/istanbuljs.git", "directory": "packages/istanbul-lib-source-maps"}, "keywords": ["istanbul", "sourcemaps", "sourcemap", "source", "maps"], "engines": {"node": ">=10"}}