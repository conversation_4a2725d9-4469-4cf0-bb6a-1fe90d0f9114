import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  Alert,
  Platform
} from 'react-native';
import axios from 'axios';

const { width, height } = Dimensions.get('window');

// API Configuration
// Use IP address for mobile devices, localhost for web
const API_BASE_URL = Platform.OS === 'web'
  ? 'http://localhost:5000/api'
  : 'http://**************:5000/api';

export default function App() {
  // Authentication state - BYPASS AUTHENTICATION FOR DIRECT DASHBOARD ACCESS
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Set to true for direct access
  const [currentUser, setCurrentUser] = useState({
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    role: 'admin'
  }); // Set default admin user
  const [authScreen, setAuthScreen] = useState('login'); // 'login' or 'register'
  const [authLoading, setAuthLoading] = useState(false);

  // Login form state
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');

  // Registration form state
  const [regFormData, setRegFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    adminCode: ''
  });

  // Dashboard state
  const [activeSection, setActiveSection] = useState('Dashboard');
  const [searchText, setSearchText] = useState('');
  const [dashboardStats, setDashboardStats] = useState({
    totalUsers: 0,
    activeVendors: 0,
    totalBookings: 0,
    totalEvents: 0
  });
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [userStats, setUserStats] = useState({});
  const [vendorStats, setVendorStats] = useState({});

  const menuItems = [
    { name: 'Dashboard', icon: '📊' },
    { name: 'User Management', icon: '👥' },
    { name: 'Vendor', icon: '🏢' },
    { name: 'Review & Categories', icon: '⭐' },
    { name: 'Pricing', icon: '💰' },
    { name: 'Bookings Online', icon: '📚' },
    { name: 'Feedback & Review', icon: '💬' },
    { name: 'System Settings', icon: '⚙️' },
  ];

  // Authentication functions
  const handleLogin = async () => {
    if (!loginEmail || !loginPassword) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      setAuthLoading(true);
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: loginEmail,
        password: loginPassword
      });

      if (response.data.success) {
        const { user, token } = response.data.data;
        setCurrentUser(user);
        setIsAuthenticated(true);

        // Store token for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        Alert.alert('Success', 'Login successful!');

        // Load dashboard data after login
        fetchDashboardStats();
      }
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || 'Login failed';
      Alert.alert('Login Failed', message);
    } finally {
      setAuthLoading(false);
    }
  };

  const handleRegister = async () => {
    const { firstName, lastName, email, password, confirmPassword, adminCode } = regFormData;

    if (!firstName || !lastName || !email || !password || !confirmPassword || !adminCode) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    try {
      setAuthLoading(true);
      const response = await axios.post(`${API_BASE_URL}/auth/register`, regFormData);

      if (response.data.success) {
        const { user, token } = response.data.data;
        setCurrentUser(user);
        setIsAuthenticated(true);

        // Store token for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        Alert.alert('Success', 'Registration successful!');

        // Load dashboard data after registration
        fetchDashboardStats();
      }
    } catch (error) {
      console.error('Registration error:', error);
      const message = error.response?.data?.message || 'Registration failed';
      Alert.alert('Registration Failed', message);
    } finally {
      setAuthLoading(false);
    }
  };

  const updateRegFormData = (field, value) => {
    setRegFormData(prev => ({ ...prev, [field]: value }));
  };

  // Toggle between direct dashboard and authentication
  const toggleAuthMode = () => {
    if (isAuthenticated) {
      // Switch to authentication mode
      setIsAuthenticated(false);
      setCurrentUser(null);
      setActiveSection('Dashboard');
      setLoginEmail('');
      setLoginPassword('');
      setRegFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        phone: '',
        adminCode: ''
      });
      delete axios.defaults.headers.common['Authorization'];
      Alert.alert('Info', 'Switched to authentication mode');
    } else {
      // Switch to direct dashboard mode
      setIsAuthenticated(true);
      setCurrentUser({
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'admin'
      });
      Alert.alert('Info', 'Switched to direct dashboard mode');
    }
  };

  const logout = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
    setActiveSection('Dashboard');
    setLoginEmail('');
    setLoginPassword('');
    setRegFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
      adminCode: ''
    });
    delete axios.defaults.headers.common['Authorization'];
    Alert.alert('Success', 'Logged out successfully');
  };

  // Fetch dashboard stats from backend
  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/dashboard/stats`);
      if (response.data.success) {
        setDashboardStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      Alert.alert('Error', 'Failed to fetch dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  // Fetch users from backend
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/users`);
      if (response.data.success) {
        setUsers(response.data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Error', 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  // Fetch vendors from backend
  const fetchVendors = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/vendors`);
      if (response.data.success) {
        setVendors(response.data.data.vendors);
      }
    } catch (error) {
      console.error('Error fetching vendors:', error);
      Alert.alert('Error', 'Failed to fetch vendors');
    } finally {
      setLoading(false);
    }
  };

  // Fetch user statistics
  const fetchUserStats = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/users/stats`);
      if (response.data.success) {
        setUserStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
    }
  };

  // Fetch vendor statistics
  const fetchVendorStats = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/vendors/stats`);
      if (response.data.success) {
        setVendorStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching vendor stats:', error);
    }
  };

  // Approve vendor
  const approveVendor = async (vendorId) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/vendors/${vendorId}/approve`, {
        notes: 'Approved via admin dashboard'
      });
      if (response.data.success) {
        Alert.alert('Success', 'Vendor approved successfully');
        fetchVendors(); // Refresh vendor list
        fetchDashboardStats(); // Refresh dashboard stats
      }
    } catch (error) {
      console.error('Error approving vendor:', error);
      Alert.alert('Error', 'Failed to approve vendor');
    }
  };

  // Reject vendor
  const rejectVendor = async (vendorId, reason) => {
    try {
      const response = await axios.put(`${API_BASE_URL}/vendors/${vendorId}/reject`, {
        reason: reason || 'Rejected via admin dashboard',
        notes: 'Rejected via admin dashboard'
      });
      if (response.data.success) {
        Alert.alert('Success', 'Vendor rejected successfully');
        fetchVendors(); // Refresh vendor list
        fetchDashboardStats(); // Refresh dashboard stats
      }
    } catch (error) {
      console.error('Error rejecting vendor:', error);
      Alert.alert('Error', 'Failed to reject vendor');
    }
  };

  // Test backend connection
  const testBackendConnection = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);
      console.log('Backend connection successful:', response.data);
    } catch (error) {
      console.error('Backend connection failed:', error);
      Alert.alert('Connection Error', 'Unable to connect to backend server');
    }
  };

  // Load data when component mounts
  useEffect(() => {
    testBackendConnection();
    fetchDashboardStats();
  }, []);

  // Load data when section changes
  useEffect(() => {
    if (activeSection === 'User Management') {
      fetchUsers();
      fetchUserStats();
    } else if (activeSection === 'Vendor') {
      fetchVendors();
      fetchVendorStats();
    }
  }, [activeSection]);

  const renderSidebar = () => (
    <View style={styles.sidebar}>
      <View style={styles.sidebarHeader}>
        <Text style={styles.logo}>EventEase</Text>
      </View>
      <ScrollView style={styles.menuContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.menuItem,
              activeSection === item.name && styles.activeMenuItem
            ]}
            onPress={() => setActiveSection(item.name)}
          >
            <Text style={styles.menuIcon}>{item.icon}</Text>
            <Text style={[
              styles.menuText,
              activeSection === item.name && styles.activeMenuText
            ]}>
              {item.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderTopBar = () => (
    <View style={styles.topBar}>
      <View style={styles.logoSection}>
        <Text style={styles.topLogo}>EventEase</Text>
      </View>

      <View style={styles.searchSection}>
        <TextInput
          style={styles.searchBar}
          placeholder="Search Bar"
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>

      <View style={styles.profileSection}>
        <TouchableOpacity style={styles.anyDropdown}>
          <Text style={styles.anyText}>Any</Text>
          <Text style={styles.dropdownArrow}>▼</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.toggleButton} onPress={toggleAuthMode}>
          <Text style={styles.toggleButtonText}>
            🔓 Direct Dashboard
          </Text>
        </TouchableOpacity>

        <View style={styles.profileInfo}>
          <Text style={styles.profileName}>{currentUser?.firstName || 'Admin'}</Text>
          <TouchableOpacity onPress={logout}>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderMainContent = () => (
    <View style={styles.mainContent}>
      <Text style={styles.contentTitle}>{activeSection}</Text>
      <View style={styles.contentArea}>
        {activeSection === 'Dashboard' && renderDashboard()}
        {activeSection === 'User Management' && renderUserManagement()}
        {activeSection === 'Vendor' && renderVendor()}
        {activeSection === 'Review & Categories' && renderReviewCategories()}
        {activeSection === 'Pricing' && renderPricing()}
        {activeSection === 'Bookings Online' && renderBookings()}
        {activeSection === 'Feedback & Review' && renderFeedback()}
        {activeSection === 'System Settings' && renderSettings()}
      </View>
    </View>
  );

  const renderDashboard = () => (
    <View style={styles.dashboardContent}>
      <Text style={styles.sectionText}>Dashboard Overview</Text>
      {loading ? (
        <Text style={styles.loadingText}>Loading...</Text>
      ) : (
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardStats.totalUsers}</Text>
            <Text style={styles.statLabel}>Total Users</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardStats.activeVendors}</Text>
            <Text style={styles.statLabel}>Active Vendors</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardStats.totalBookings}</Text>
            <Text style={styles.statLabel}>Total Bookings</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{dashboardStats.totalEvents}</Text>
            <Text style={styles.statLabel}>Events</Text>
          </View>
        </View>
      )}
      <TouchableOpacity
        style={styles.refreshButton}
        onPress={fetchDashboardStats}
      >
        <Text style={styles.refreshButtonText}>🔄 Refresh Data</Text>
      </TouchableOpacity>
    </View>
  );

  const renderUserManagement = () => (
    <ScrollView style={styles.dashboardContent}>
      <Text style={styles.sectionText}>User Management System</Text>

      {/* User Statistics */}
      {userStats.overview && (
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{userStats.overview.totalUsers}</Text>
            <Text style={styles.statLabel}>Total Users</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{userStats.overview.activeUsers}</Text>
            <Text style={styles.statLabel}>Active Users</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{userStats.overview.adminUsers}</Text>
            <Text style={styles.statLabel}>Admin Users</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{userStats.overview.recentUsers}</Text>
            <Text style={styles.statLabel}>Recent (30d)</Text>
          </View>
        </View>
      )}

      {/* Users List */}
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>All Users</Text>
        {loading ? (
          <Text style={styles.loadingText}>Loading users...</Text>
        ) : (
          users.map((user, index) => (
            <View key={user._id || index} style={styles.listItem}>
              <View style={styles.userInfo}>
                <Text style={styles.userName}>{user.firstName} {user.lastName}</Text>
                <Text style={styles.userEmail}>{user.email}</Text>
                <View style={styles.userMeta}>
                  <Text style={[styles.userRole, { backgroundColor: user.role === 'admin' ? '#e74c3c' : user.role === 'moderator' ? '#f39c12' : '#3498db' }]}>
                    {user.role?.toUpperCase()}
                  </Text>
                  <Text style={[styles.userStatus, { backgroundColor: user.status === 'active' ? '#27ae60' : '#95a5a6' }]}>
                    {user.status?.toUpperCase()}
                  </Text>
                </View>
              </View>
              <View style={styles.userActions}>
                <TouchableOpacity style={styles.actionButton}>
                  <Text style={styles.actionButtonText}>Edit</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.actionButton, styles.deleteButton]}>
                  <Text style={styles.actionButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </View>

      <TouchableOpacity
        style={styles.refreshButton}
        onPress={() => {
          fetchUsers();
          fetchUserStats();
        }}
      >
        <Text style={styles.refreshButtonText}>🔄 Refresh Users</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderVendor = () => (
    <ScrollView style={styles.dashboardContent}>
      <Text style={styles.sectionText}>Vendor Management</Text>

      {/* Vendor Statistics */}
      {vendorStats.overview && (
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{vendorStats.overview.totalVendors}</Text>
            <Text style={styles.statLabel}>Total Vendors</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{vendorStats.overview.pendingVendors}</Text>
            <Text style={styles.statLabel}>Pending</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{vendorStats.overview.approvedVendors}</Text>
            <Text style={styles.statLabel}>Approved</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{vendorStats.overview.rejectedVendors}</Text>
            <Text style={styles.statLabel}>Rejected</Text>
          </View>
        </View>
      )}

      {/* Vendors List */}
      <View style={styles.listContainer}>
        <Text style={styles.listTitle}>All Vendor Applications</Text>
        {loading ? (
          <Text style={styles.loadingText}>Loading vendors...</Text>
        ) : (
          vendors.map((vendor, index) => (
            <View key={vendor._id || index} style={styles.listItem}>
              <View style={styles.vendorInfo}>
                <Text style={styles.vendorName}>{vendor.businessName}</Text>
                <Text style={styles.vendorEmail}>{vendor.email}</Text>
                <Text style={styles.vendorCategory}>{vendor.businessInfo?.category}</Text>
                <Text style={styles.vendorContact}>
                  Contact: {vendor.contactPerson?.firstName} {vendor.contactPerson?.lastName}
                </Text>
                <View style={styles.vendorMeta}>
                  <Text style={[
                    styles.vendorStatus,
                    {
                      backgroundColor:
                        vendor.status === 'approved' ? '#27ae60' :
                        vendor.status === 'pending' ? '#f39c12' :
                        vendor.status === 'rejected' ? '#e74c3c' : '#95a5a6'
                    }
                  ]}>
                    {vendor.status?.toUpperCase()}
                  </Text>
                  {vendor.rating?.average > 0 && (
                    <Text style={styles.vendorRating}>
                      ⭐ {vendor.rating.average.toFixed(1)} ({vendor.rating.count} reviews)
                    </Text>
                  )}
                </View>
              </View>
              <View style={styles.vendorActions}>
                {vendor.status === 'pending' && (
                  <>
                    <TouchableOpacity
                      style={[styles.actionButton, styles.approveButton]}
                      onPress={() => approveVendor(vendor._id)}
                    >
                      <Text style={styles.actionButtonText}>✓ Approve</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[styles.actionButton, styles.rejectButton]}
                      onPress={() => rejectVendor(vendor._id, 'Rejected via admin dashboard')}
                    >
                      <Text style={styles.actionButtonText}>✗ Reject</Text>
                    </TouchableOpacity>
                  </>
                )}
                {vendor.status === 'approved' && (
                  <TouchableOpacity style={[styles.actionButton, styles.suspendButton]}>
                    <Text style={styles.actionButtonText}>Suspend</Text>
                  </TouchableOpacity>
                )}
                <TouchableOpacity style={styles.actionButton}>
                  <Text style={styles.actionButtonText}>View Details</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))
        )}
      </View>

      <TouchableOpacity
        style={styles.refreshButton}
        onPress={() => {
          fetchVendors();
          fetchVendorStats();
        }}
      >
        <Text style={styles.refreshButtonText}>🔄 Refresh Vendors</Text>
      </TouchableOpacity>
    </ScrollView>
  );

  const renderReviewCategories = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionText}>Review & Categories</Text>
      <Text style={styles.sectionDescription}>Manage reviews and event categories</Text>
    </View>
  );

  const renderPricing = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionText}>Pricing Management</Text>
      <Text style={styles.sectionDescription}>Set and manage pricing plans</Text>
    </View>
  );

  const renderBookings = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionText}>Bookings Online</Text>
      <Text style={styles.sectionDescription}>View and manage all online bookings</Text>
    </View>
  );

  const renderFeedback = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionText}>Feedback & Review</Text>
      <Text style={styles.sectionDescription}>Monitor customer feedback and reviews</Text>
    </View>
  );

  const renderSettings = () => (
    <View style={styles.sectionContent}>
      <Text style={styles.sectionText}>System Settings</Text>
      <Text style={styles.sectionDescription}>Configure system preferences and settings</Text>
    </View>
  );

  // Login Screen Component
  const renderLoginScreen = () => (
    <View style={styles.authContainer}>
      <View style={styles.authCard}>
        <Text style={styles.authTitle}>EventEase Admin</Text>
        <Text style={styles.authSubtitle}>Sign in to your admin account</Text>

        <View style={styles.authForm}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Email Address</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your email"
              value={loginEmail}
              onChangeText={setLoginEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your password"
              value={loginPassword}
              onChangeText={setLoginPassword}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <TouchableOpacity
            style={[styles.authButton, authLoading && styles.authButtonDisabled]}
            onPress={handleLogin}
            disabled={authLoading}
          >
            <Text style={styles.authButtonText}>
              {authLoading ? 'Signing In...' : 'Sign In'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.authSwitchButton}
            onPress={() => setAuthScreen('register')}
          >
            <Text style={styles.authSwitchText}>
              Don't have an account? <Text style={styles.authSwitchLink}>Register here</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Registration Screen Component
  const renderRegisterScreen = () => (
    <ScrollView style={styles.authContainer}>
      <View style={styles.authCard}>
        <Text style={styles.authTitle}>EventEase Admin</Text>
        <Text style={styles.authSubtitle}>Create your admin account</Text>

        <View style={styles.authForm}>
          <View style={styles.inputRow}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={styles.inputLabel}>First Name</Text>
              <TextInput
                style={styles.authInput}
                placeholder="First name"
                value={regFormData.firstName}
                onChangeText={(value) => updateRegFormData('firstName', value)}
                autoCapitalize="words"
              />
            </View>

            <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
              <Text style={styles.inputLabel}>Last Name</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Last name"
                value={regFormData.lastName}
                onChangeText={(value) => updateRegFormData('lastName', value)}
                autoCapitalize="words"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Email Address</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your email"
              value={regFormData.email}
              onChangeText={(value) => updateRegFormData('email', value)}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Phone Number</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your phone number"
              value={regFormData.phone}
              onChangeText={(value) => updateRegFormData('phone', value)}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your password"
              value={regFormData.password}
              onChangeText={(value) => updateRegFormData('password', value)}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Confirm Password</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Confirm your password"
              value={regFormData.confirmPassword}
              onChangeText={(value) => updateRegFormData('confirmPassword', value)}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Admin Registration Code</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter admin code (ADMIN2024)"
              value={regFormData.adminCode}
              onChangeText={(value) => updateRegFormData('adminCode', value)}
              autoCapitalize="characters"
              autoCorrect={false}
            />
            <Text style={styles.inputHint}>Contact your system administrator for the admin code</Text>
          </View>

          <TouchableOpacity
            style={[styles.authButton, authLoading && styles.authButtonDisabled]}
            onPress={handleRegister}
            disabled={authLoading}
          >
            <Text style={styles.authButtonText}>
              {authLoading ? 'Creating Account...' : 'Create Admin Account'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.authSwitchButton}
            onPress={() => setAuthScreen('login')}
          >
            <Text style={styles.authSwitchText}>
              Already have an account? <Text style={styles.authSwitchLink}>Sign in here</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );

  // Show authentication screens if not authenticated
  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        {authScreen === 'login' ? renderLoginScreen() : renderRegisterScreen()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.appContainer}>
        {renderTopBar()}
        <View style={styles.bodyContainer}>
          {renderSidebar()}
          {renderMainContent()}
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  appContainer: {
    flex: 1,
  },
  topBar: {
    height: 70,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  logoSection: {
    flex: 1,
  },
  topLogo: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  searchSection: {
    flex: 2,
    paddingHorizontal: 20,
  },
  searchBar: {
    height: 40,
    backgroundColor: '#f8f9fa',
    borderRadius: 20,
    paddingHorizontal: 15,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  profileSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  anyDropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 15,
  },
  anyText: {
    fontSize: 14,
    color: '#495057',
    marginRight: 5,
  },
  dropdownArrow: {
    fontSize: 10,
    color: '#6c757d',
  },
  toggleButton: {
    backgroundColor: '#27ae60',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 15,
  },
  toggleButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  profileInfo: {
    alignItems: 'flex-end',
  },
  profileName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 2,
  },
  logoutText: {
    fontSize: 12,
    color: '#dc3545',
    textDecorationLine: 'underline',
  },
  bodyContainer: {
    flex: 1,
    flexDirection: 'row',
  },
  sidebar: {
    width: 250,
    backgroundColor: '#2c3e50',
    borderRightWidth: 1,
    borderRightColor: '#34495e',
  },
  sidebarHeader: {
    height: 60,
    backgroundColor: '#34495e',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#4a5f7a',
  },
  logo: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  menuContainer: {
    flex: 1,
    paddingTop: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginHorizontal: 10,
    marginVertical: 2,
    borderRadius: 8,
  },
  activeMenuItem: {
    backgroundColor: '#3498db',
  },
  menuIcon: {
    fontSize: 18,
    marginRight: 12,
    width: 25,
  },
  menuText: {
    fontSize: 14,
    color: '#ecf0f1',
    flex: 1,
  },
  activeMenuText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  mainContent: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2c3e50',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
    backgroundColor: '#f8f9fa',
  },
  contentArea: {
    flex: 1,
    padding: 20,
  },
  dashboardContent: {
    flex: 1,
  },
  sectionText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 10,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#6c757d',
    lineHeight: 24,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  statCard: {
    width: '48%',
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    borderLeftWidth: 4,
    borderLeftColor: '#3498db',
  },
  statNumber: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    color: '#6c757d',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  sectionContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#6c757d',
    textAlign: 'center',
    marginTop: 50,
  },
  refreshButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 20,
    alignSelf: 'center',
  },
  refreshButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  listContainer: {
    marginTop: 30,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 15,
  },
  listItem: {
    backgroundColor: '#ffffff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 8,
  },
  userMeta: {
    flexDirection: 'row',
    gap: 8,
  },
  userRole: {
    fontSize: 10,
    color: '#ffffff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontWeight: '600',
    textAlign: 'center',
    minWidth: 60,
  },
  userStatus: {
    fontSize: 10,
    color: '#ffffff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontWeight: '600',
    textAlign: 'center',
    minWidth: 60,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    backgroundColor: '#3498db',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    minWidth: 60,
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  deleteButton: {
    backgroundColor: '#e74c3c',
  },
  approveButton: {
    backgroundColor: '#27ae60',
  },
  rejectButton: {
    backgroundColor: '#e74c3c',
  },
  suspendButton: {
    backgroundColor: '#f39c12',
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 4,
  },
  vendorEmail: {
    fontSize: 14,
    color: '#6c757d',
    marginBottom: 4,
  },
  vendorCategory: {
    fontSize: 14,
    color: '#3498db',
    marginBottom: 4,
    fontWeight: '500',
  },
  vendorContact: {
    fontSize: 12,
    color: '#6c757d',
    marginBottom: 8,
  },
  vendorMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  vendorStatus: {
    fontSize: 10,
    color: '#ffffff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    fontWeight: '600',
    textAlign: 'center',
    minWidth: 70,
  },
  vendorRating: {
    fontSize: 12,
    color: '#f39c12',
    fontWeight: '500',
  },
  vendorActions: {
    flexDirection: 'column',
    gap: 6,
    alignItems: 'flex-end',
  },
  // Authentication Styles
  authContainer: {
    flex: 1,
    backgroundColor: '#f5f6fa',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 30,
    width: '100%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  authTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#2c3e50',
    textAlign: 'center',
    marginBottom: 8,
  },
  authSubtitle: {
    fontSize: 16,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 30,
  },
  authForm: {
    width: '100%',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2c3e50',
    marginBottom: 8,
  },
  authInput: {
    height: 50,
    borderWidth: 1,
    borderColor: '#e9ecef',
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: '#f8f9fa',
    color: '#2c3e50',
  },
  inputHint: {
    fontSize: 12,
    color: '#6c757d',
    marginTop: 5,
    fontStyle: 'italic',
  },
  authButton: {
    backgroundColor: '#3498db',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  authButtonDisabled: {
    backgroundColor: '#95a5a6',
  },
  authButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  authSwitchButton: {
    alignItems: 'center',
    marginTop: 10,
  },
  authSwitchText: {
    fontSize: 14,
    color: '#6c757d',
  },
  authSwitchLink: {
    color: '#3498db',
    fontWeight: '600',
  },
});
