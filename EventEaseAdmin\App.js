import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  Text,
  View,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Dimensions,
  Alert,
  Platform,
  ActivityIndicator
} from 'react-native';
import axios from 'axios';

const { width, height } = Dimensions.get('window');

// API Configuration
const API_BASE_URL = Platform.OS === 'web'
  ? 'http://localhost:3001/api'
  : 'http://**************:3001/api';

export default function App() {
  // Authentication state
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [authScreen, setAuthScreen] = useState('login');
  const [authLoading, setAuthLoading] = useState(false);

  // Login form state
  const [loginEmail, setLoginEmail] = useState('<EMAIL>');
  const [loginPassword, setLoginPassword] = useState('SuperAdmin123!');

  // Registration form state
  const [regFormData, setRegFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    adminCode: 'ADMIN2024'
  });

  // Dashboard state
  const [activeSection, setActiveSection] = useState('Dashboard');
  const [searchText, setSearchText] = useState('');
  const [dashboardStats, setDashboardStats] = useState({
    overview: {
      totalUsers: 0,
      totalVendors: 0,
      totalBookings: 0,
      totalEvents: 0,
      totalRevenue: 0,
      activeUsers: 0,
      pendingVendors: 0
    }
  });
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [vendors, setVendors] = useState([]);

  const menuItems = [
    { name: 'Dashboard', icon: '📊', color: '#667eea' },
    { name: 'User Management', icon: '👥', color: '#764ba2' },
    { name: 'Vendor Management', icon: '🏢', color: '#f093fb' },
    { name: 'Events', icon: '🎉', color: '#4facfe' },
    { name: 'Bookings', icon: '📅', color: '#43e97b' },
    { name: 'Analytics', icon: '📈', color: '#fa709a' },
    { name: 'Settings', icon: '⚙️', color: '#ffecd2' },
    { name: 'Reports', icon: '📋', color: '#a8edea' },
  ];

  // Authentication functions
  const handleLogin = async () => {
    if (!loginEmail || !loginPassword) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      setAuthLoading(true);
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email: loginEmail,
        password: loginPassword
      });

      if (response.data.success) {
        const { user, token } = response.data.data;
        setCurrentUser(user);
        setIsAuthenticated(true);

        // Store token for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        Alert.alert('Success', 'Welcome to EventEase Admin!');
        fetchDashboardStats();
      }
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || 'Login failed';
      Alert.alert('Login Failed', message);
    } finally {
      setAuthLoading(false);
    }
  };

  const handleRegister = async () => {
    const { firstName, lastName, email, password, confirmPassword, adminCode } = regFormData;

    if (!firstName || !lastName || !email || !password || !confirmPassword || !adminCode) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return;
    }

    try {
      setAuthLoading(true);
      const response = await axios.post(`${API_BASE_URL}/auth/register`, regFormData);

      if (response.data.success) {
        const { user, token } = response.data.data;
        setCurrentUser(user);
        setIsAuthenticated(true);

        // Store token for future requests
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        Alert.alert('Success', 'Account created successfully!');
        fetchDashboardStats();
      }
    } catch (error) {
      console.error('Registration error:', error);
      const message = error.response?.data?.message || 'Registration failed';
      Alert.alert('Registration Failed', message);
    } finally {
      setAuthLoading(false);
    }
  };

  const updateRegFormData = (field, value) => {
    setRegFormData(prev => ({ ...prev, [field]: value }));
  };

  const logout = () => {
    setIsAuthenticated(false);
    setCurrentUser(null);
    setActiveSection('Dashboard');
    setLoginEmail('');
    setLoginPassword('');
    setRegFormData({
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
      adminCode: ''
    });
    delete axios.defaults.headers.common['Authorization'];
    Alert.alert('Success', 'Logged out successfully');
  };

  // Fetch dashboard stats from backend
  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/dashboard/stats`);
      if (response.data.success) {
        setDashboardStats(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      // Use mock data if backend is not available
      setDashboardStats({
        overview: {
          totalUsers: 1247,
          totalVendors: 89,
          totalBookings: 456,
          totalEvents: 123,
          totalRevenue: 89750,
          activeUsers: 1156,
          pendingVendors: 12
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch users from backend
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/users`);
      if (response.data.success) {
        setUsers(response.data.data.users);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      // Mock data
      setUsers([
        { _id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>', role: 'admin', status: 'active' },
        { _id: '2', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', role: 'user', status: 'active' },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch vendors from backend
  const fetchVendors = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/vendors`);
      if (response.data.success) {
        setVendors(response.data.data.vendors);
      }
    } catch (error) {
      console.error('Error fetching vendors:', error);
      // Mock data
      setVendors([
        { _id: '1', businessName: 'Elite Catering', email: '<EMAIL>', status: 'pending', businessInfo: { category: 'Catering' } },
        { _id: '2', businessName: 'Perfect Photos', email: '<EMAIL>', status: 'approved', businessInfo: { category: 'Photography' } },
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Test backend connection
  const testBackendConnection = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/health`);
      console.log('Backend connection successful:', response.data);
    } catch (error) {
      console.error('Backend connection failed:', error);
      console.log('Using mock data for demonstration');
    }
  };

  // Load data when component mounts
  useEffect(() => {
    testBackendConnection();
    if (isAuthenticated) {
      fetchDashboardStats();
    }
  }, [isAuthenticated]);

  // Load data when section changes
  useEffect(() => {
    if (isAuthenticated) {
      if (activeSection === 'User Management') {
        fetchUsers();
      } else if (activeSection === 'Vendor Management') {
        fetchVendors();
      }
    }
  }, [activeSection, isAuthenticated]);

  // Professional Login Screen
  const renderLoginScreen = () => (
    <View style={styles.authContainer}>
      <View style={styles.authCard}>
        <View style={styles.authHeader}>
          <Text style={styles.authLogo}>EventEase</Text>
          <Text style={styles.authSubtitle}>Admin Dashboard</Text>
        </View>

        <View style={styles.authForm}>
          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Email Address</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your email"
              value={loginEmail}
              onChangeText={setLoginEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.inputLabel}>Password</Text>
            <TextInput
              style={styles.authInput}
              placeholder="Enter your password"
              value={loginPassword}
              onChangeText={setLoginPassword}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <TouchableOpacity
            style={[styles.authButton, authLoading && styles.authButtonDisabled]}
            onPress={handleLogin}
            disabled={authLoading}
          >
            {authLoading ? (
              <ActivityIndicator color="#ffffff" />
            ) : (
              <Text style={styles.authButtonText}>Sign In</Text>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.authSwitchButton}
            onPress={() => setAuthScreen('register')}
          >
            <Text style={styles.authSwitchText}>
              Don't have an account? <Text style={styles.authSwitchLink}>Register here</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  // Professional Registration Screen
  const renderRegisterScreen = () => (
    <View style={styles.authContainer}>
      <ScrollView contentContainerStyle={styles.authScrollContainer}>
        <View style={styles.authCard}>
          <View style={styles.authHeader}>
            <Text style={styles.authLogo}>EventEase</Text>
            <Text style={styles.authSubtitle}>Create Admin Account</Text>
          </View>

          <View style={styles.authForm}>
            <View style={styles.inputRow}>
              <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
                <Text style={styles.inputLabel}>First Name</Text>
                <TextInput
                  style={styles.authInput}
                  placeholder="First name"
                  value={regFormData.firstName}
                  onChangeText={(value) => updateRegFormData('firstName', value)}
                  autoCapitalize="words"
                />
              </View>

              <View style={[styles.inputGroup, { flex: 1, marginLeft: 10 }]}>
                <Text style={styles.inputLabel}>Last Name</Text>
                <TextInput
                  style={styles.authInput}
                  placeholder="Last name"
                  value={regFormData.lastName}
                  onChangeText={(value) => updateRegFormData('lastName', value)}
                  autoCapitalize="words"
                />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email Address</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Enter your email"
                value={regFormData.email}
                onChangeText={(value) => updateRegFormData('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Enter your phone number"
                value={regFormData.phone}
                onChangeText={(value) => updateRegFormData('phone', value)}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Password</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Enter your password"
                value={regFormData.password}
                onChangeText={(value) => updateRegFormData('password', value)}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Confirm Password</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Confirm your password"
                value={regFormData.confirmPassword}
                onChangeText={(value) => updateRegFormData('confirmPassword', value)}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Admin Registration Code</Text>
              <TextInput
                style={styles.authInput}
                placeholder="Enter admin code"
                value={regFormData.adminCode}
                onChangeText={(value) => updateRegFormData('adminCode', value)}
                autoCapitalize="characters"
                autoCorrect={false}
              />
              <Text style={styles.inputHint}>Contact your system administrator for the admin code</Text>
            </View>

            <TouchableOpacity
              style={[styles.authButton, authLoading && styles.authButtonDisabled]}
              onPress={handleRegister}
              disabled={authLoading}
            >
              {authLoading ? (
                <ActivityIndicator color="#ffffff" />
              ) : (
                <Text style={styles.authButtonText}>Create Account</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.authSwitchButton}
              onPress={() => setAuthScreen('login')}
            >
              <Text style={styles.authSwitchText}>
                Already have an account? <Text style={styles.authSwitchLink}>Sign in here</Text>
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );

  // Show authentication screens if not authenticated
  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="light" />
        {authScreen === 'login' ? renderLoginScreen() : renderRegisterScreen()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.appContainer}>
        {renderTopBar()}
        <View style={styles.bodyContainer}>
          {renderSidebar()}
          {renderMainContent()}
        </View>
      </View>
    </SafeAreaView>
  );
}

// Professional Top Bar
const renderTopBar = () => (
  <View style={styles.topBar}>
    <View style={styles.logoSection}>
      <Text style={styles.topLogo}>EventEase</Text>
      <Text style={styles.topSubtitle}>Admin Dashboard</Text>
    </View>

    <View style={styles.searchSection}>
      <View style={styles.searchContainer}>
        <Text style={styles.searchIcon}>🔍</Text>
        <TextInput
          style={styles.searchBar}
          placeholder="Search anything..."
          value={searchText}
          onChangeText={setSearchText}
        />
      </View>
    </View>

    <View style={styles.profileSection}>
      <View style={styles.notificationButton}>
        <Text style={styles.notificationIcon}>🔔</Text>
        <View style={styles.notificationBadge}>
          <Text style={styles.notificationCount}>3</Text>
        </View>
      </View>

      <View style={styles.profileInfo}>
        <Text style={styles.profileName}>{currentUser?.firstName || 'Admin'} {currentUser?.lastName || 'User'}</Text>
        <Text style={styles.profileRole}>{currentUser?.role || 'Administrator'}</Text>
      </View>

      <TouchableOpacity style={styles.profileAvatar}>
        <Text style={styles.avatarText}>{(currentUser?.firstName?.[0] || 'A')}</Text>
      </TouchableOpacity>

      <TouchableOpacity onPress={logout} style={styles.logoutButton}>
        <Text style={styles.logoutIcon}>🚪</Text>
      </TouchableOpacity>
    </View>
  </View>
);

// Professional Sidebar
const renderSidebar = () => (
  <View style={styles.sidebar}>
    <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
      {menuItems.map((item, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.menuItem,
            activeSection === item.name && styles.activeMenuItem
          ]}
          onPress={() => setActiveSection(item.name)}
        >
          <View style={[
            styles.menuItemGradient,
            activeSection === item.name && { backgroundColor: item.color }
          ]}>
            <Text style={[
              styles.menuIcon,
              activeSection === item.name && styles.activeMenuIcon
            ]}>{item.icon}</Text>
            <Text style={[
              styles.menuText,
              activeSection === item.name && styles.activeMenuText
            ]}>
              {item.name}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </ScrollView>
  </View>
);

// Professional Main Content
const renderMainContent = () => (
  <View style={styles.mainContent}>
    <View style={styles.contentHeader}>
      <Text style={styles.contentTitle}>{activeSection}</Text>
      <View style={styles.breadcrumb}>
        <Text style={styles.breadcrumbText}>Dashboard</Text>
        <Text style={styles.breadcrumbSeparator}>›</Text>
        <Text style={styles.breadcrumbActive}>{activeSection}</Text>
      </View>
    </View>

    <ScrollView style={styles.contentArea} showsVerticalScrollIndicator={false}>
      {activeSection === 'Dashboard' && renderDashboard()}
      {activeSection === 'User Management' && renderUserManagement()}
      {activeSection === 'Vendor Management' && renderVendorManagement()}
      {activeSection === 'Events' && renderEvents()}
      {activeSection === 'Bookings' && renderBookings()}
      {activeSection === 'Analytics' && renderAnalytics()}
      {activeSection === 'Settings' && renderSettings()}
      {activeSection === 'Reports' && renderReports()}
    </ScrollView>
  </View>
);

// Professional Dashboard
const renderDashboard = () => (
  <View style={styles.dashboardContent}>
    {/* Stats Cards */}
    <View style={styles.statsGrid}>
      <View style={[styles.statCard, { backgroundColor: '#667eea' }]}>
        <View style={styles.statCardContent}>
          <Text style={styles.statNumber}>{dashboardStats.overview?.totalUsers || 0}</Text>
          <Text style={styles.statLabel}>Total Users</Text>
          <Text style={styles.statChange}>+12% from last month</Text>
        </View>
        <Text style={styles.statIcon}>👥</Text>
      </View>

      <View style={[styles.statCard, { backgroundColor: '#f093fb' }]}>
        <View style={styles.statCardContent}>
          <Text style={styles.statNumber}>{dashboardStats.overview?.totalVendors || 0}</Text>
          <Text style={styles.statLabel}>Total Vendors</Text>
          <Text style={styles.statChange}>+8% from last month</Text>
        </View>
        <Text style={styles.statIcon}>🏢</Text>
      </View>

      <View style={[styles.statCard, { backgroundColor: '#4facfe' }]}>
        <View style={styles.statCardContent}>
          <Text style={styles.statNumber}>{dashboardStats.overview?.totalBookings || 0}</Text>
          <Text style={styles.statLabel}>Total Bookings</Text>
          <Text style={styles.statChange}>+15% from last month</Text>
        </View>
        <Text style={styles.statIcon}>📅</Text>
      </View>

      <View style={[styles.statCard, { backgroundColor: '#43e97b' }]}>
        <View style={styles.statCardContent}>
          <Text style={styles.statNumber}>${(dashboardStats.overview?.totalRevenue || 0).toLocaleString()}</Text>
          <Text style={styles.statLabel}>Total Revenue</Text>
          <Text style={styles.statChange}>+22% from last month</Text>
        </View>
        <Text style={styles.statIcon}>💰</Text>
      </View>
    </View>

    {/* Quick Actions */}
    <View style={styles.quickActions}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <View style={styles.actionGrid}>
        <TouchableOpacity style={styles.actionCard}>
          <Text style={styles.actionIcon}>➕</Text>
          <Text style={styles.actionText}>Add User</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionCard}>
          <Text style={styles.actionIcon}>✅</Text>
          <Text style={styles.actionText}>Approve Vendors</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionCard}>
          <Text style={styles.actionIcon}>📊</Text>
          <Text style={styles.actionText}>View Reports</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionCard}>
          <Text style={styles.actionIcon}>⚙️</Text>
          <Text style={styles.actionText}>Settings</Text>
        </TouchableOpacity>
      </View>
    </View>

    {/* Recent Activity */}
    <View style={styles.recentActivity}>
      <Text style={styles.sectionTitle}>Recent Activity</Text>
      <View style={styles.activityList}>
        <View style={styles.activityItem}>
          <Text style={styles.activityIcon}>👤</Text>
          <View style={styles.activityContent}>
            <Text style={styles.activityText}>New user registered: John Doe</Text>
            <Text style={styles.activityTime}>2 minutes ago</Text>
          </View>
        </View>
        <View style={styles.activityItem}>
          <Text style={styles.activityIcon}>🏢</Text>
          <View style={styles.activityContent}>
            <Text style={styles.activityText}>Vendor application submitted: Elite Catering</Text>
            <Text style={styles.activityTime}>15 minutes ago</Text>
          </View>
        </View>
        <View style={styles.activityItem}>
          <Text style={styles.activityIcon}>📅</Text>
          <View style={styles.activityContent}>
            <Text style={styles.activityText}>New booking created for Wedding Event</Text>
            <Text style={styles.activityTime}>1 hour ago</Text>
          </View>
        </View>
      </View>
    </View>
  </View>
);

// Placeholder components for other sections
const renderUserManagement = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>User Management</Text>
    <Text style={styles.sectionDescription}>Manage all users and their permissions</Text>
    {loading ? (
      <ActivityIndicator size="large" color="#667eea" style={styles.loader} />
    ) : (
      <View style={styles.usersList}>
        {users.map((user, index) => (
          <View key={user._id || index} style={styles.userCard}>
            <View style={styles.userAvatar}>
              <Text style={styles.userAvatarText}>{user.firstName?.[0]}{user.lastName?.[0]}</Text>
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{user.firstName} {user.lastName}</Text>
              <Text style={styles.userEmail}>{user.email}</Text>
              <Text style={styles.userRole}>{user.role}</Text>
            </View>
            <View style={styles.userActions}>
              <TouchableOpacity style={styles.actionBtn}>
                <Text style={styles.actionBtnText}>Edit</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>
    )}
  </View>
);

const renderVendorManagement = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>Vendor Management</Text>
    <Text style={styles.sectionDescription}>Review and manage vendor applications</Text>
    {loading ? (
      <ActivityIndicator size="large" color="#667eea" style={styles.loader} />
    ) : (
      <View style={styles.vendorsList}>
        {vendors.map((vendor, index) => (
          <View key={vendor._id || index} style={styles.vendorCard}>
            <View style={styles.vendorInfo}>
              <Text style={styles.vendorName}>{vendor.businessName}</Text>
              <Text style={styles.vendorEmail}>{vendor.email}</Text>
              <Text style={styles.vendorCategory}>{vendor.businessInfo?.category}</Text>
            </View>
            <View style={styles.vendorStatus}>
              <Text style={[styles.statusBadge, { backgroundColor: vendor.status === 'approved' ? '#43e97b' : vendor.status === 'pending' ? '#f093fb' : '#f5576c' }]}>
                {vendor.status}
              </Text>
            </View>
            <View style={styles.vendorActions}>
              {vendor.status === 'pending' && (
                <>
                  <TouchableOpacity style={[styles.actionBtn, styles.approveBtn]}>
                    <Text style={styles.actionBtnText}>Approve</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={[styles.actionBtn, styles.rejectBtn]}>
                    <Text style={styles.actionBtnText}>Reject</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </View>
        ))}
      </View>
    )}
  </View>
);

const renderEvents = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>Events Management</Text>
    <Text style={styles.sectionDescription}>Manage all events and their details</Text>
  </View>
);

const renderBookings = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>Bookings Management</Text>
    <Text style={styles.sectionDescription}>View and manage all bookings</Text>
  </View>
);

const renderAnalytics = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>Analytics & Reports</Text>
    <Text style={styles.sectionDescription}>View detailed analytics and generate reports</Text>
  </View>
);

const renderSettings = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>System Settings</Text>
    <Text style={styles.sectionDescription}>Configure system preferences and settings</Text>
  </View>
);

const renderReports = () => (
  <View style={styles.sectionContent}>
    <Text style={styles.sectionTitle}>Reports</Text>
    <Text style={styles.sectionDescription}>Generate and download various reports</Text>
  </View>
);

// Professional Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  appContainer: {
    flex: 1,
  },

  // Authentication Styles
  authContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#667eea',
  },
  authScrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  authCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 24,
    padding: 40,
    width: '100%',
    maxWidth: 500,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 20,
    },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 20,
  },
  authHeader: {
    alignItems: 'center',
    marginBottom: 40,
  },
  authLogo: {
    fontSize: 36,
    fontWeight: '800',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -1,
  },
  authSubtitle: {
    fontSize: 18,
    color: '#718096',
    fontWeight: '500',
  },
  authForm: {
    width: '100%',
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputRow: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 8,
    letterSpacing: 0.5,
  },
  authInput: {
    height: 56,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    borderRadius: 16,
    paddingHorizontal: 20,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#2d3748',
    fontWeight: '500',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  inputHint: {
    fontSize: 12,
    color: '#a0aec0',
    marginTop: 6,
    fontStyle: 'italic',
  },
  authButton: {
    backgroundColor: '#667eea',
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 24,
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  authButtonDisabled: {
    backgroundColor: '#a0aec0',
    shadowOpacity: 0.1,
  },
  authButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  authSwitchButton: {
    alignItems: 'center',
    marginTop: 16,
  },
  authSwitchText: {
    fontSize: 16,
    color: '#718096',
    fontWeight: '500',
  },
  authSwitchLink: {
    color: '#667eea',
    fontWeight: '700',
  },

  // Top Bar Styles
  topBar: {
    height: 80,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 30,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  logoSection: {
    flex: 1,
  },
  topLogo: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1a202c',
    letterSpacing: -1,
  },
  topSubtitle: {
    fontSize: 14,
    color: '#718096',
    fontWeight: '500',
    marginTop: 2,
  },
  searchSection: {
    flex: 2,
    paddingHorizontal: 30,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f7fafc',
    borderRadius: 20,
    paddingHorizontal: 20,
    height: 48,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  searchIcon: {
    fontSize: 18,
    marginRight: 12,
    color: '#a0aec0',
  },
  searchBar: {
    flex: 1,
    fontSize: 16,
    color: '#2d3748',
    fontWeight: '500',
  },
  profileSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  notificationButton: {
    position: 'relative',
    marginRight: 20,
    padding: 8,
  },
  notificationIcon: {
    fontSize: 20,
    color: '#718096',
  },
  notificationBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#f56565',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    fontSize: 10,
    color: '#ffffff',
    fontWeight: '700',
  },
  profileInfo: {
    alignItems: 'flex-end',
    marginRight: 16,
  },
  profileName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2d3748',
    marginBottom: 2,
  },
  profileRole: {
    fontSize: 12,
    color: '#718096',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  profileAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: '700',
    color: '#ffffff',
  },
  logoutButton: {
    padding: 8,
  },
  logoutIcon: {
    fontSize: 20,
    color: '#e53e3e',
  },

  // Body Container
  bodyContainer: {
    flex: 1,
    flexDirection: 'row',
  },

  // Sidebar Styles
  sidebar: {
    width: 280,
    backgroundColor: '#ffffff',
    borderRightWidth: 1,
    borderRightColor: '#e2e8f0',
    shadowColor: '#000',
    shadowOffset: {
      width: 2,
      height: 0,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  menuContainer: {
    flex: 1,
    paddingTop: 20,
    paddingHorizontal: 16,
  },
  menuItem: {
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  menuItemGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 16,
  },
  activeMenuItem: {
    shadowColor: '#667eea',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  menuIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 28,
    color: '#718096',
  },
  activeMenuIcon: {
    color: '#ffffff',
  },
  menuText: {
    fontSize: 16,
    color: '#2d3748',
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  activeMenuText: {
    color: '#ffffff',
    fontWeight: '700',
  },

  // Main Content Styles
  mainContent: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  contentHeader: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 30,
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  contentTitle: {
    fontSize: 32,
    fontWeight: '800',
    color: '#1a202c',
    marginBottom: 8,
    letterSpacing: -1,
  },
  breadcrumb: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  breadcrumbText: {
    fontSize: 14,
    color: '#a0aec0',
    fontWeight: '500',
  },
  breadcrumbSeparator: {
    fontSize: 14,
    color: '#a0aec0',
    marginHorizontal: 8,
  },
  breadcrumbActive: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '600',
  },
  contentArea: {
    flex: 1,
    padding: 30,
  },

  // Dashboard Styles
  dashboardContent: {
    flex: 1,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 40,
  },
  statCard: {
    width: '48%',
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  statCardContent: {
    flex: 1,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: '800',
    color: '#ffffff',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '600',
    marginBottom: 8,
  },
  statChange: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  statIcon: {
    fontSize: 32,
    opacity: 0.8,
  },

  // Quick Actions
  quickActions: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1a202c',
    marginBottom: 20,
    letterSpacing: -0.5,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: '23%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: 12,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2d3748',
    textAlign: 'center',
  },

  // Recent Activity
  recentActivity: {
    marginBottom: 40,
  },
  activityList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f7fafc',
  },
  activityIcon: {
    fontSize: 20,
    marginRight: 16,
    width: 32,
    textAlign: 'center',
  },
  activityContent: {
    flex: 1,
  },
  activityText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2d3748',
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 12,
    color: '#a0aec0',
    fontWeight: '500',
  },

  // Section Content
  sectionContent: {
    flex: 1,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#718096',
    marginBottom: 30,
    fontWeight: '500',
  },
  loader: {
    marginTop: 50,
  },

  // User Management
  usersList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f7fafc',
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userAvatarText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#ffffff',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2d3748',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 4,
  },
  userRole: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  userActions: {
    flexDirection: 'row',
  },
  actionBtn: {
    backgroundColor: '#667eea',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 12,
    marginLeft: 8,
  },
  actionBtnText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  approveBtn: {
    backgroundColor: '#48bb78',
  },
  rejectBtn: {
    backgroundColor: '#f56565',
  },

  // Vendor Management
  vendorsList: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  vendorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f7fafc',
  },
  vendorInfo: {
    flex: 1,
  },
  vendorName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#2d3748',
    marginBottom: 4,
  },
  vendorEmail: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 4,
  },
  vendorCategory: {
    fontSize: 12,
    color: '#667eea',
    fontWeight: '600',
  },
  vendorStatus: {
    marginRight: 16,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
    textTransform: 'uppercase',
  },
  vendorActions: {
    flexDirection: 'row',
  },
});