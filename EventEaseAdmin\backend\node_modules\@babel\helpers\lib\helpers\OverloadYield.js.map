{"version": 3, "names": ["_OverloadYield", "value", "kind", "v", "k"], "sources": ["../../src/helpers/OverloadYield.ts"], "sourcesContent": ["/* @minVersion 7.18.14 */\n\n/*\n * 'kind' is an enum:\n *   0 => This yield was an await expression\n *   1 => This yield comes from yield*\n */\n\n// _OverloadYield is actually a class\ndeclare class _OverloadYield<T = any> {\n  constructor(value: T, /** 0: await 1: delegate */ kind: 0 | 1);\n\n  v: T;\n  /** 0: await 1: delegate */\n  k: 0 | 1;\n}\n\n// The actual implementation of _OverloadYield starts here\nfunction _OverloadYield<T>(\n  this: _OverloadYield<T>,\n  value: T,\n  /** 0: await 1: delegate */ kind: 0 | 1,\n) {\n  this.v = value;\n  this.k = kind;\n}\n\nexport { _OverloadYield as default };\n"], "mappings": ";;;;;;AAkBA,SAASA,cAAcA,CAErBC,KAAQ,EACoBC,IAAW,EACvC;EACA,IAAI,CAACC,CAAC,GAAGF,KAAK;EACd,IAAI,CAACG,CAAC,GAAGF,IAAI;AACf", "ignoreList": []}